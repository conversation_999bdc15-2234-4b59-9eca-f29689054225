# **52.1 - FUNDAMENTOS DE PROMESAS EN CLASES**

## **📖 INTRODUCCIÓN**

Las promesas representan la evolución natural del manejo asíncrono en JavaScript, transformando el caótico "callback hell" en un flujo elegante y predecible que se integra perfectamente con la programación orientada a objetos. En el contexto de las clases ES6, las promesas no son simplemente una herramienta de conveniencia, sino el fundamento arquitectónico que permite crear sistemas robustos, escalables y mantenibles que manejan operaciones asíncronas de manera profesional. Dominar las promesas en el contexto de OOP te permitirá diseñar APIs fluidas, implementar patrones de retry sofisticados, crear sistemas de cache inteligentes y construir arquitecturas reactivas que respondan elegantemente a eventos asincrónicos. En aplicaciones empresariales modernas, las promesas son esenciales para integrar servicios externos, manejar bases de datos, procesar archivos y coordinar múltiples operaciones concurrentes, convirtiendo el código asíncrono en una sinfonía orquestada de operaciones que se ejecutan con precisión y gracia. La maestría en promesas dentro de clases no solo mejorará la calidad de tu código, sino que te posicionará como un arquitecto de software capaz de diseñar sistemas que escalan y evolucionan con las demandas del negocio.

## **💻 CÓDIGO DE EJEMPLO**

```javascript
// ===== SISTEMA AVANZADO DE GESTIÓN DE PROMESAS EN CLASES =====

class AdvancedPromiseManager {
    constructor(options = {}) {
        // Configuración del gestor
        this.maxConcurrency = options.maxConcurrency || 5;
        this.defaultTimeout = options.defaultTimeout || 30000;
        this.retryAttempts = options.retryAttempts || 3;
        this.retryDelay = options.retryDelay || 1000;
        
        // Estado interno
        this.activePromises = new Map();
        this.promiseQueue = [];
        this.completedPromises = new Map();
        this.failedPromises = new Map();
        
        // Métricas y estadísticas
        this.metrics = {
            totalPromises: 0,
            successfulPromises: 0,
            failedPromises: 0,
            averageExecutionTime: 0,
            concurrentPeak: 0
        };
        
        // Event listeners para monitoreo
        this.eventListeners = new Map();
        
        // Cleanup automático
        this.cleanupInterval = setInterval(() => this.cleanup(), 60000);
    }
    
    // ===== CREACIÓN Y GESTIÓN DE PROMESAS =====
    
    /**
     * Crea una promesa con configuración avanzada
     * @param {Function} executor - Función ejecutora de la promesa
     * @param {Object} options - Opciones de configuración
     * @returns {Promise} Promesa configurada con timeout y retry
     */
    createPromise(executor, options = {}) {
        const promiseId = this.generatePromiseId();
        const config = {
            id: promiseId,
            timeout: options.timeout || this.defaultTimeout,
            retryAttempts: options.retryAttempts || this.retryAttempts,
            retryDelay: options.retryDelay || this.retryDelay,
            priority: options.priority || 'normal',
            tags: options.tags || [],
            createdAt: Date.now()
        };
        
        // Crear promesa con timeout automático
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Promise ${promiseId} timed out after ${config.timeout}ms`));
            }, config.timeout);
        });
        
        // Crear promesa principal con retry automático
        const mainPromise = this.createRetryablePromise(executor, config);
        
        // Combinar con timeout usando Promise.race
        const finalPromise = Promise.race([mainPromise, timeoutPromise])
            .then(result => {
                this.handlePromiseSuccess(promiseId, result);
                return result;
            })
            .catch(error => {
                this.handlePromiseFailure(promiseId, error);
                throw error;
            });
        
        // Registrar promesa activa
        this.activePromises.set(promiseId, {
            promise: finalPromise,
            config: config,
            startTime: Date.now()
        });
        
        this.metrics.totalPromises++;
        this.updateConcurrencyMetrics();
        this.emit('promiseCreated', { id: promiseId, config });
        
        return finalPromise;
    }
    
    /**
     * Crea una promesa con capacidad de retry automático
     * @param {Function} executor - Función ejecutora
     * @param {Object} config - Configuración de la promesa
     * @returns {Promise} Promesa con retry
     */
    createRetryablePromise(executor, config) {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            
            const attemptExecution = () => {
                attempts++;
                
                try {
                    // Ejecutar la función con manejo de errores
                    const result = executor(resolve, reject);
                    
                    // Si el executor retorna una promesa, manejarla
                    if (result && typeof result.then === 'function') {
                        result
                            .then(resolve)
                            .catch(handleError);
                    }
                } catch (error) {
                    handleError(error);
                }
            };
            
            const handleError = (error) => {
                if (attempts < config.retryAttempts) {
                    this.emit('promiseRetry', {
                        id: config.id,
                        attempt: attempts,
                        error: error.message
                    });
                    
                    // Retry con delay exponencial
                    const delay = config.retryDelay * Math.pow(2, attempts - 1);
                    setTimeout(attemptExecution, delay);
                } else {
                    reject(error);
                }
            };
            
            // Iniciar primera ejecución
            attemptExecution();
        });
    }
    
    // ===== GESTIÓN DE CONCURRENCIA =====
    
    /**
     * Ejecuta promesas con control de concurrencia
     * @param {Array} promiseFactories - Array de funciones que retornan promesas
     * @param {Object} options - Opciones de ejecución
     * @returns {Promise<Array>} Resultados de todas las promesas
     */
    async executeConcurrent(promiseFactories, options = {}) {
        const concurrency = options.concurrency || this.maxConcurrency;
        const failFast = options.failFast !== false;
        const results = [];
        const errors = [];
        
        // Dividir en lotes según la concurrencia
        for (let i = 0; i < promiseFactories.length; i += concurrency) {
            const batch = promiseFactories.slice(i, i + concurrency);
            
            try {
                const batchPromises = batch.map((factory, index) => 
                    this.createPromise(factory, {
                        ...options,
                        tags: [...(options.tags || []), `batch-${Math.floor(i / concurrency)}`]
                    }).catch(error => {
                        errors.push({ index: i + index, error });
                        if (failFast) throw error;
                        return null; // Placeholder para mantener índices
                    })
                );
                
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
                
            } catch (error) {
                if (failFast) {
                    throw new Error(`Batch execution failed: ${error.message}`);
                }
            }
        }
        
        return {
            results,
            errors,
            successCount: results.filter(r => r !== null).length,
            errorCount: errors.length
        };
    }
    
    /**
     * Ejecuta promesas en secuencia con control de flujo
     * @param {Array} promiseFactories - Array de funciones que retornan promesas
     * @param {Object} options - Opciones de ejecución
     * @returns {Promise<Array>} Resultados en orden secuencial
     */
    async executeSequential(promiseFactories, options = {}) {
        const results = [];
        const stopOnError = options.stopOnError !== false;
        
        for (let i = 0; i < promiseFactories.length; i++) {
            try {
                const result = await this.createPromise(promiseFactories[i], {
                    ...options,
                    tags: [...(options.tags || []), `sequence-${i}`]
                });
                
                results.push(result);
                
                // Callback de progreso si está definido
                if (options.onProgress) {
                    options.onProgress({
                        completed: i + 1,
                        total: promiseFactories.length,
                        result
                    });
                }
                
            } catch (error) {
                if (stopOnError) {
                    throw new Error(`Sequential execution stopped at index ${i}: ${error.message}`);
                }
                results.push(null);
            }
        }
        
        return results;
    }
    
    // ===== UTILIDADES Y HELPERS =====
    
    /**
     * Convierte callback-style functions a promesas
     * @param {Function} fn - Función con callback
     * @param {Object} context - Contexto de ejecución
     * @returns {Function} Función promisificada
     */
    promisify(fn, context = null) {
        return (...args) => {
            return this.createPromise((resolve, reject) => {
                const callback = (error, result) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(result);
                    }
                };
                
                try {
                    fn.call(context, ...args, callback);
                } catch (error) {
                    reject(error);
                }
            });
        };
    }
    
    /**
     * Crea una promesa que se resuelve después de un delay
     * @param {number} ms - Milisegundos de delay
     * @param {*} value - Valor a retornar
     * @returns {Promise} Promesa con delay
     */
    delay(ms, value = undefined) {
        return this.createPromise((resolve) => {
            setTimeout(() => resolve(value), ms);
        }, { timeout: ms + 1000 });
    }
    
    /**
     * Implementa timeout para cualquier promesa
     * @param {Promise} promise - Promesa a la cual aplicar timeout
     * @param {number} ms - Milisegundos de timeout
     * @returns {Promise} Promesa con timeout
     */
    timeout(promise, ms) {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Operation timed out after ${ms}ms`));
            }, ms);
        });
        
        return Promise.race([promise, timeoutPromise]);
    }
    
    // ===== MANEJO DE EVENTOS Y MÉTRICAS =====
    
    /**
     * Registra un event listener
     * @param {string} event - Nombre del evento
     * @param {Function} listener - Función listener
     */
    on(event, listener) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(listener);
    }
    
    /**
     * Emite un evento
     * @param {string} event - Nombre del evento
     * @param {*} data - Datos del evento
     */
    emit(event, data) {
        const listeners = this.eventListeners.get(event) || [];
        listeners.forEach(listener => {
            try {
                listener(data);
            } catch (error) {
                console.error(`Error in event listener for ${event}:`, error);
            }
        });
    }
    
    /**
     * Maneja el éxito de una promesa
     * @param {string} promiseId - ID de la promesa
     * @param {*} result - Resultado de la promesa
     */
    handlePromiseSuccess(promiseId, result) {
        const promiseData = this.activePromises.get(promiseId);
        if (promiseData) {
            const executionTime = Date.now() - promiseData.startTime;
            
            this.completedPromises.set(promiseId, {
                ...promiseData,
                result,
                executionTime,
                completedAt: Date.now()
            });
            
            this.activePromises.delete(promiseId);
            this.metrics.successfulPromises++;
            this.updateAverageExecutionTime(executionTime);
            
            this.emit('promiseResolved', {
                id: promiseId,
                result,
                executionTime
            });
        }
    }
    
    /**
     * Maneja el fallo de una promesa
     * @param {string} promiseId - ID de la promesa
     * @param {Error} error - Error de la promesa
     */
    handlePromiseFailure(promiseId, error) {
        const promiseData = this.activePromises.get(promiseId);
        if (promiseData) {
            const executionTime = Date.now() - promiseData.startTime;
            
            this.failedPromises.set(promiseId, {
                ...promiseData,
                error,
                executionTime,
                failedAt: Date.now()
            });
            
            this.activePromises.delete(promiseId);
            this.metrics.failedPromises++;
            
            this.emit('promiseRejected', {
                id: promiseId,
                error: error.message,
                executionTime
            });
        }
    }
    
    // ===== MÉTODOS PRIVADOS =====
    
    generatePromiseId() {
        return `promise_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    updateConcurrencyMetrics() {
        const currentConcurrency = this.activePromises.size;
        if (currentConcurrency > this.metrics.concurrentPeak) {
            this.metrics.concurrentPeak = currentConcurrency;
        }
    }
    
    updateAverageExecutionTime(newTime) {
        const total = this.metrics.successfulPromises;
        const currentAvg = this.metrics.averageExecutionTime;
        this.metrics.averageExecutionTime = ((currentAvg * (total - 1)) + newTime) / total;
    }
    
    cleanup() {
        const now = Date.now();
        const maxAge = 300000; // 5 minutos
        
        // Limpiar promesas completadas antiguas
        for (const [id, data] of this.completedPromises) {
            if (now - data.completedAt > maxAge) {
                this.completedPromises.delete(id);
            }
        }
        
        // Limpiar promesas fallidas antiguas
        for (const [id, data] of this.failedPromises) {
            if (now - data.failedAt > maxAge) {
                this.failedPromises.delete(id);
            }
        }
    }
    
    // ===== API PÚBLICA DE CONSULTA =====
    
    /**
     * Obtiene métricas actuales del gestor
     * @returns {Object} Métricas detalladas
     */
    getMetrics() {
        return {
            ...this.metrics,
            activePromises: this.activePromises.size,
            completedPromises: this.completedPromises.size,
            failedPromises: this.failedPromises.size,
            successRate: this.metrics.totalPromises > 0 ? 
                (this.metrics.successfulPromises / this.metrics.totalPromises * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * Obtiene el estado de todas las promesas activas
     * @returns {Array} Array con información de promesas activas
     */
    getActivePromises() {
        return Array.from(this.activePromises.entries()).map(([id, data]) => ({
            id,
            config: data.config,
            runningTime: Date.now() - data.startTime,
            estimatedCompletion: data.startTime + data.config.timeout
        }));
    }
    
    /**
     * Cancela todas las promesas activas
     * @returns {number} Número de promesas canceladas
     */
    cancelAllPromises() {
        const count = this.activePromises.size;
        this.activePromises.clear();
        this.emit('allPromisesCancelled', { count });
        return count;
    }
    
    /**
     * Destructor del gestor
     */
    destroy() {
        this.cancelAllPromises();
        clearInterval(this.cleanupInterval);
        this.eventListeners.clear();
        this.completedPromises.clear();
        this.failedPromises.clear();
    }
}

// ===== EJEMPLO DE USO PRÁCTICO =====

// Crear instancia del gestor
const promiseManager = new AdvancedPromiseManager({
    maxConcurrency: 3,
    defaultTimeout: 5000,
    retryAttempts: 2
});

// Configurar listeners para monitoreo
promiseManager.on('promiseCreated', (data) => {
    console.log(`✨ Promesa creada: ${data.id}`);
});

promiseManager.on('promiseResolved', (data) => {
    console.log(`✅ Promesa resuelta: ${data.id} en ${data.executionTime}ms`);
});

promiseManager.on('promiseRejected', (data) => {
    console.log(`❌ Promesa falló: ${data.id} - ${data.error}`);
});

// Ejemplo 1: Promesa simple con retry
async function ejemploPromesaSimple() {
    try {
        const resultado = await promiseManager.createPromise((resolve, reject) => {
            // Simular operación que puede fallar
            if (Math.random() > 0.7) {
                resolve('¡Operación exitosa!');
            } else {
                reject(new Error('Operación falló'));
            }
        }, {
            retryAttempts: 3,
            retryDelay: 1000,
            tags: ['ejemplo', 'simple']
        });
        
        console.log('Resultado:', resultado);
    } catch (error) {
        console.error('Error final:', error.message);
    }
}

// Ejemplo 2: Ejecución concurrente
async function ejemploConcurrencia() {
    const tareas = Array.from({ length: 10 }, (_, i) => 
        (resolve) => {
            setTimeout(() => {
                resolve(`Tarea ${i + 1} completada`);
            }, Math.random() * 2000);
        }
    );
    
    const resultado = await promiseManager.executeConcurrent(tareas, {
        concurrency: 3,
        tags: ['concurrencia', 'ejemplo']
    });
    
    console.log('Resultados concurrentes:', resultado);
}

// Ejemplo 3: Promisificación de función callback
function operacionCallback(data, callback) {
    setTimeout(() => {
        if (data.valid) {
            callback(null, `Procesado: ${data.value}`);
        } else {
            callback(new Error('Datos inválidos'));
        }
    }, 1000);
}

const operacionPromise = promiseManager.promisify(operacionCallback);

// Ejecutar ejemplos
ejemploPromesaSimple();
ejemploConcurrencia();

operacionPromise({ valid: true, value: 'test' })
    .then(resultado => console.log('Promisificado:', resultado))
    .catch(error => console.error('Error promisificado:', error));

// Mostrar métricas cada 5 segundos
setInterval(() => {
    console.log('📊 Métricas:', promiseManager.getMetrics());
}, 5000);
```

## **🔍 EXPLICACIÓN EXHAUSTIVA DEL CÓDIGO**

### **Líneas 3-25: Arquitectura del Constructor**
```javascript
constructor(options = {}) {
    this.maxConcurrency = options.maxConcurrency || 5;
    this.defaultTimeout = options.defaultTimeout || 30000;
    // ... configuración inicial
}
```

El constructor establece una **arquitectura robusta** para la gestión de promesas. La configuración por defecto (`maxConcurrency: 5`, `defaultTimeout: 30000`) está optimizada para aplicaciones web típicas, pero permite personalización total. El uso de **Maps** para `activePromises`, `completedPromises` y `failedPromises` proporciona acceso O(1) y mejor gestión de memoria que los objetos planos.

### **Líneas 35-75: Sistema de Creación de Promesas**
```javascript
createPromise(executor, options = {}) {
    const promiseId = this.generatePromiseId();
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Promise ${promiseId} timed out`)), config.timeout);
    });
}
```

Este método implementa el **patrón Decorator** para promesas, añadiendo funcionalidades como timeout automático, retry y tracking. El uso de `Promise.race()` entre la promesa principal y el timeout garantiza que ninguna promesa quede colgada indefinidamente, un problema común en aplicaciones de producción.

### **Líneas 95-135: Mecanismo de Retry Inteligente**
```javascript
createRetryablePromise(executor, config) {
    const handleError = (error) => {
        if (attempts < config.retryAttempts) {
            const delay = config.retryDelay * Math.pow(2, attempts - 1);
            setTimeout(attemptExecution, delay);
        }
    };
}
```

El sistema de retry implementa **backoff exponencial**, una técnica crucial para evitar el "thundering herd problem" en servicios distribuidos. Cada reintento duplica el delay, reduciendo la carga en servicios externos y mejorando las probabilidades de éxito.

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de Integración de APIs Externas**
En aplicaciones empresariales que consumen múltiples APIs externas (pagos, autenticación, datos de terceros), este gestor permite manejar timeouts variables, reintentos automáticos y control de concurrencia para evitar rate limiting. Por ejemplo, integrar Stripe, Auth0 y una API de geolocalización simultáneamente sin sobrecargar ningún servicio.

### **Caso 2: Procesamiento de Archivos en Lotes**
Para aplicaciones que procesan grandes volúmenes de archivos (imágenes, documentos, videos), el control de concurrencia evita saturar la memoria mientras el sistema de retry maneja fallos temporales de red o storage. Ideal para plataformas de e-learning o sistemas de gestión documental.

### **Caso 3: Microservicios y Orquestación**
En arquitecturas de microservicios, este gestor actúa como un coordinador inteligente que maneja la comunicación entre servicios, implementa circuit breakers básicos y proporciona métricas detalladas para monitoring y debugging.

## **⚠️ ERRORES COMUNES**

### **Error 1: Memory Leaks por Promesas No Limpiadas**
```javascript
// ❌ INCORRECTO - Acumula promesas indefinidamente
class BadPromiseManager {
    constructor() {
        this.allPromises = []; // Nunca se limpia
    }
    
    addPromise(promise) {
        this.allPromises.push(promise); // Memory leak
    }
}

// ✅ CORRECTO - Limpieza automática
cleanup() {
    const now = Date.now();
    const maxAge = 300000; // 5 minutos
    
    for (const [id, data] of this.completedPromises) {
        if (now - data.completedAt > maxAge) {
            this.completedPromises.delete(id);
        }
    }
}
```

### **Error 2: Race Conditions en Concurrencia**
```javascript
// ❌ INCORRECTO - No controla concurrencia
async function badConcurrency(tasks) {
    return Promise.all(tasks.map(task => task())); // Puede saturar recursos
}

// ✅ CORRECTO - Control de concurrencia
async function executeConcurrent(promiseFactories, options = {}) {
    const concurrency = options.concurrency || this.maxConcurrency;
    // Procesa en lotes controlados
}
```

### **Error 3: Manejo Inadecuado de Timeouts**
```javascript
// ❌ INCORRECTO - Timeout sin cleanup
function badTimeout(promise, ms) {
    return Promise.race([
        promise,
        new Promise((_, reject) => setTimeout(reject, ms))
    ]); // El timeout puede quedar activo
}

// ✅ CORRECTO - Timeout con cleanup
timeout(promise, ms) {
    let timeoutId;
    const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => reject(new Error(`Timeout after ${ms}ms`)), ms);
    });
    
    return Promise.race([promise, timeoutPromise])
        .finally(() => clearTimeout(timeoutId));
}
```

## **💡 RECOMENDACIONES PARA DOMINAR PROMESAS EN OOP**

### **1. Implementa Patrones de Resilencia**
Desarrolla sistemas que fallen graciosamente usando circuit breakers, bulkheads y timeouts adaptativos. Estudia librerías como Hystrix para entender patrones de resilencia avanzados.

### **2. Monitorea Performance Activamente**
Implementa métricas detalladas que incluyan percentiles de latencia, tasas de error y throughput. Usa herramientas como Prometheus o DataDog para visualizar el comportamiento de tus promesas en producción.

### **3. Diseña APIs Fluidas**
Crea interfaces que permitan method chaining y configuración declarativa. Inspírate en librerías como Axios o Fetch API para diseñar APIs intuitivas.

### **4. Optimiza para Diferentes Escenarios**
Ajusta configuraciones según el contexto: timeouts cortos para UI responsiva, timeouts largos para operaciones de background, concurrencia alta para I/O bound tasks.

### **5. Implementa Testing Comprehensivo**
Crea tests que cubran escenarios de éxito, fallo, timeout y retry. Usa herramientas como Jest con fake timers para probar comportamientos temporales.

## **🤔 PREGUNTAS SOCRÁTICAS PARA REFLEXIÓN**

- ¿Cómo diseñarías un sistema de prioridades para promesas que permita ejecutar tareas críticas antes que las de baja prioridad?
- ¿Qué estrategias implementarías para manejar promesas que dependen unas de otras en un grafo de dependencias complejo?
- ¿Cómo optimizarías el sistema para diferentes patrones de carga (picos vs carga constante)?
- ¿De qué manera integrarías este gestor con sistemas de observabilidad como OpenTelemetry para tracing distribuido?

## **📊 VISUALIZACIONES ANATÓMICAS**

### **Anatomía del Sistema de Promesas**

![Anatomía del Sistema de Promesas](SVG/promise_system_anatomy.svg)

### **Flujo de Ejecución con Retry y Timeout**

![Flujo de Ejecución](SVG/promise_execution_flow.svg)

### **Gestión de Concurrencia y Cola de Promesas**

![Gestión de Concurrencia](SVG/promise_concurrency_management.svg)

### **Mapa Mental: Promesas en OOP**

![Mapa Mental Promesas](SVG/promise_mindmap.svg)
