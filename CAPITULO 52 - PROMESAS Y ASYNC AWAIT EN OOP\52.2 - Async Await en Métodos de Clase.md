# **52.2 - ASYNC/AWAIT EN MÉTODOS DE CLASE**

## **📖 INTRODUCCIÓN**

Async/await representa la culminación evolutiva del manejo asíncrono en JavaScript, transformando el código basado en promesas en una sintaxis que se lee como código síncrono pero mantiene toda la potencia de la programación asíncrona. En el contexto de la programación orientada a objetos, async/await no es simplemente azúcar sintáctico, sino una herramienta arquitectónica que permite diseñar clases que manejan operaciones asíncronas de manera elegante, mantenible y escalable. La integración de async/await en métodos de clase abre las puertas a patrones de diseño sofisticados como Active Record asíncrono, Repository Pattern con operaciones no bloqueantes, y Service Layer con manejo de estado complejo. En aplicaciones empresariales modernas, dominar async/await en OOP es esencial para crear APIs que respondan eficientemente, sistemas de cache inteligentes que se actualizan en background, y arquitecturas de microservicios que coordinan múltiples operaciones distribuidas. La maestría en async/await dentro de clases te permitirá construir sistemas que no solo funcionan correctamente bajo carga, sino que también proporcionan una experiencia de usuario fluida y un código que otros desarrolladores pueden entender, mantener y extender con confianza.

## **💻 CÓDIGO DE EJEMPLO**

```javascript
// ===== SISTEMA AVANZADO DE GESTIÓN DE DATOS CON ASYNC/AWAIT =====

class AdvancedDataManager {
    constructor(options = {}) {
        // Configuración del gestor
        this.apiBaseUrl = options.apiBaseUrl || 'https://api.example.com';
        this.cacheTimeout = options.cacheTimeout || 300000; // 5 minutos
        this.maxRetries = options.maxRetries || 3;
        this.batchSize = options.batchSize || 10;
        
        // Estado interno
        this.cache = new Map();
        this.pendingRequests = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        
        // Métricas de performance
        this.metrics = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageResponseTime: 0,
            errorRate: 0
        };
        
        // Configuración de headers por defecto
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
        
        // Inicializar procesamiento de cola
        this.startQueueProcessor();
    }
    
    // ===== MÉTODOS ASYNC BÁSICOS =====
    
    /**
     * Obtiene datos de una entidad específica con cache inteligente
     * @param {string} entityType - Tipo de entidad (users, products, etc.)
     * @param {string|number} id - ID de la entidad
     * @param {Object} options - Opciones de configuración
     * @returns {Promise<Object>} Datos de la entidad
     */
    async getData(entityType, id, options = {}) {
        const startTime = Date.now();
        const cacheKey = `${entityType}:${id}`;
        const forceRefresh = options.forceRefresh || false;
        
        try {
            // Verificar cache si no se fuerza refresh
            if (!forceRefresh && this.cache.has(cacheKey)) {
                const cachedData = this.cache.get(cacheKey);
                
                if (Date.now() - cachedData.timestamp < this.cacheTimeout) {
                    this.metrics.cacheHits++;
                    this.updateMetrics(startTime, true);
                    
                    return {
                        ...cachedData.data,
                        _metadata: {
                            source: 'cache',
                            timestamp: cachedData.timestamp,
                            responseTime: Date.now() - startTime
                        }
                    };
                }
            }
            
            // Verificar si hay una request pendiente para evitar duplicados
            if (this.pendingRequests.has(cacheKey)) {
                return await this.pendingRequests.get(cacheKey);
            }
            
            // Crear nueva request
            const requestPromise = this.executeRequest('GET', `/${entityType}/${id}`, null, options);
            this.pendingRequests.set(cacheKey, requestPromise);
            
            try {
                const data = await requestPromise;
                
                // Guardar en cache
                this.cache.set(cacheKey, {
                    data,
                    timestamp: Date.now()
                });
                
                this.metrics.cacheMisses++;
                this.updateMetrics(startTime, true);
                
                return {
                    ...data,
                    _metadata: {
                        source: 'api',
                        timestamp: Date.now(),
                        responseTime: Date.now() - startTime
                    }
                };
                
            } finally {
                // Limpiar request pendiente
                this.pendingRequests.delete(cacheKey);
            }
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            throw this.enhanceError(error, 'getData', { entityType, id });
        }
    }
    
    /**
     * Crea una nueva entidad con validación y retry automático
     * @param {string} entityType - Tipo de entidad
     * @param {Object} data - Datos de la entidad
     * @param {Object} options - Opciones de configuración
     * @returns {Promise<Object>} Entidad creada
     */
    async createEntity(entityType, data, options = {}) {
        const startTime = Date.now();
        const validateData = options.validate !== false;
        
        try {
            // Validación de datos si está habilitada
            if (validateData) {
                await this.validateEntityData(entityType, data);
            }
            
            // Preparar datos para envío
            const payload = this.preparePayload(data, options);
            
            // Ejecutar request con retry automático
            const result = await this.executeWithRetry(
                () => this.executeRequest('POST', `/${entityType}`, payload, options),
                this.maxRetries
            );
            
            // Invalidar cache relacionado
            await this.invalidateRelatedCache(entityType, result.id);
            
            this.updateMetrics(startTime, true);
            
            return {
                ...result,
                _metadata: {
                    created: true,
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime
                }
            };
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            throw this.enhanceError(error, 'createEntity', { entityType, data });
        }
    }
    
    /**
     * Actualiza una entidad existente con merge inteligente
     * @param {string} entityType - Tipo de entidad
     * @param {string|number} id - ID de la entidad
     * @param {Object} updates - Datos a actualizar
     * @param {Object} options - Opciones de configuración
     * @returns {Promise<Object>} Entidad actualizada
     */
    async updateEntity(entityType, id, updates, options = {}) {
        const startTime = Date.now();
        const mergeStrategy = options.mergeStrategy || 'shallow';
        
        try {
            // Obtener datos actuales si se requiere merge
            let currentData = null;
            if (mergeStrategy !== 'replace') {
                currentData = await this.getData(entityType, id, { forceRefresh: false });
            }
            
            // Preparar datos finales según estrategia de merge
            const finalData = this.mergeData(currentData, updates, mergeStrategy);
            
            // Validar datos finales
            if (options.validate !== false) {
                await this.validateEntityData(entityType, finalData);
            }
            
            // Ejecutar actualización
            const result = await this.executeWithRetry(
                () => this.executeRequest('PUT', `/${entityType}/${id}`, finalData, options),
                this.maxRetries
            );
            
            // Actualizar cache
            const cacheKey = `${entityType}:${id}`;
            this.cache.set(cacheKey, {
                data: result,
                timestamp: Date.now()
            });
            
            this.updateMetrics(startTime, true);
            
            return {
                ...result,
                _metadata: {
                    updated: true,
                    mergeStrategy,
                    timestamp: Date.now(),
                    responseTime: Date.now() - startTime
                }
            };
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            throw this.enhanceError(error, 'updateEntity', { entityType, id, updates });
        }
    }
    
    // ===== OPERACIONES EN LOTE =====
    
    /**
     * Procesa múltiples operaciones en lotes optimizados
     * @param {Array} operations - Array de operaciones
     * @param {Object} options - Opciones de procesamiento
     * @returns {Promise<Array>} Resultados de las operaciones
     */
    async processBatch(operations, options = {}) {
        const batchSize = options.batchSize || this.batchSize;
        const continueOnError = options.continueOnError !== false;
        const results = [];
        const errors = [];
        
        try {
            // Dividir operaciones en lotes
            for (let i = 0; i < operations.length; i += batchSize) {
                const batch = operations.slice(i, i + batchSize);
                
                // Procesar lote actual
                const batchResults = await this.processSingleBatch(batch, {
                    continueOnError,
                    batchIndex: Math.floor(i / batchSize)
                });
                
                // Separar resultados exitosos de errores
                batchResults.forEach((result, index) => {
                    if (result.success) {
                        results.push({
                            index: i + index,
                            data: result.data,
                            operation: batch[index]
                        });
                    } else {
                        errors.push({
                            index: i + index,
                            error: result.error,
                            operation: batch[index]
                        });
                    }
                });
                
                // Callback de progreso si está definido
                if (options.onProgress) {
                    await options.onProgress({
                        completed: Math.min(i + batchSize, operations.length),
                        total: operations.length,
                        successCount: results.length,
                        errorCount: errors.length
                    });
                }
                
                // Delay entre lotes si está configurado
                if (options.batchDelay && i + batchSize < operations.length) {
                    await this.delay(options.batchDelay);
                }
            }
            
            return {
                results,
                errors,
                summary: {
                    total: operations.length,
                    successful: results.length,
                    failed: errors.length,
                    successRate: (results.length / operations.length * 100).toFixed(2) + '%'
                }
            };
            
        } catch (error) {
            throw this.enhanceError(error, 'processBatch', { operationsCount: operations.length });
        }
    }
    
    /**
     * Procesa un lote individual de operaciones
     * @param {Array} batch - Lote de operaciones
     * @param {Object} options - Opciones del lote
     * @returns {Promise<Array>} Resultados del lote
     */
    async processSingleBatch(batch, options = {}) {
        const promises = batch.map(async (operation, index) => {
            try {
                let result;
                
                switch (operation.type) {
                    case 'get':
                        result = await this.getData(operation.entityType, operation.id, operation.options);
                        break;
                    case 'create':
                        result = await this.createEntity(operation.entityType, operation.data, operation.options);
                        break;
                    case 'update':
                        result = await this.updateEntity(operation.entityType, operation.id, operation.data, operation.options);
                        break;
                    case 'delete':
                        result = await this.deleteEntity(operation.entityType, operation.id, operation.options);
                        break;
                    default:
                        throw new Error(`Unsupported operation type: ${operation.type}`);
                }
                
                return { success: true, data: result };
                
            } catch (error) {
                if (options.continueOnError) {
                    return { success: false, error };
                } else {
                    throw error;
                }
            }
        });
        
        return await Promise.all(promises);
    }
    
    // ===== BÚSQUEDA Y FILTRADO AVANZADO =====
    
    /**
     * Realiza búsquedas complejas con paginación y filtros
     * @param {string} entityType - Tipo de entidad
     * @param {Object} criteria - Criterios de búsqueda
     * @param {Object} options - Opciones de búsqueda
     * @returns {Promise<Object>} Resultados paginados
     */
    async searchEntities(entityType, criteria = {}, options = {}) {
        const startTime = Date.now();
        
        try {
            // Construir query parameters
            const queryParams = this.buildQueryParams(criteria, options);
            const cacheKey = `search:${entityType}:${JSON.stringify(queryParams)}`;
            
            // Verificar cache para búsquedas
            if (options.useCache !== false && this.cache.has(cacheKey)) {
                const cachedResult = this.cache.get(cacheKey);
                
                if (Date.now() - cachedResult.timestamp < (options.cacheTimeout || this.cacheTimeout)) {
                    this.metrics.cacheHits++;
                    return {
                        ...cachedResult.data,
                        _metadata: {
                            source: 'cache',
                            responseTime: Date.now() - startTime
                        }
                    };
                }
            }
            
            // Ejecutar búsqueda
            const endpoint = `/${entityType}/search?${new URLSearchParams(queryParams)}`;
            const result = await this.executeRequest('GET', endpoint, null, options);
            
            // Procesar resultados
            const processedResult = {
                data: result.data || [],
                pagination: {
                    page: result.page || 1,
                    limit: result.limit || 10,
                    total: result.total || 0,
                    totalPages: Math.ceil((result.total || 0) / (result.limit || 10))
                },
                filters: criteria,
                sort: options.sort || null
            };
            
            // Guardar en cache
            if (options.useCache !== false) {
                this.cache.set(cacheKey, {
                    data: processedResult,
                    timestamp: Date.now()
                });
            }
            
            this.updateMetrics(startTime, true);
            
            return {
                ...processedResult,
                _metadata: {
                    source: 'api',
                    responseTime: Date.now() - startTime,
                    resultsCount: processedResult.data.length
                }
            };
            
        } catch (error) {
            this.updateMetrics(startTime, false);
            throw this.enhanceError(error, 'searchEntities', { entityType, criteria });
        }
    }
    
    // ===== MÉTODOS DE UTILIDAD ASYNC =====
    
    /**
     * Ejecuta una operación con retry automático y backoff exponencial
     * @param {Function} operation - Función a ejecutar
     * @param {number} maxRetries - Número máximo de reintentos
     * @param {number} baseDelay - Delay base en ms
     * @returns {Promise} Resultado de la operación
     */
    async executeWithRetry(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                // No reintentar en el último intento
                if (attempt === maxRetries) {
                    break;
                }
                
                // No reintentar errores de cliente (4xx)
                if (error.status >= 400 && error.status < 500) {
                    break;
                }
                
                // Calcular delay con backoff exponencial
                const delay = baseDelay * Math.pow(2, attempt);
                await this.delay(delay);
            }
        }
        
        throw lastError;
    }
    
    /**
     * Valida datos de entidad de forma asíncrona
     * @param {string} entityType - Tipo de entidad
     * @param {Object} data - Datos a validar
     * @returns {Promise<boolean>} True si es válido
     */
    async validateEntityData(entityType, data) {
        // Obtener esquema de validación
        const schema = await this.getValidationSchema(entityType);
        
        // Validar estructura básica
        const structureValid = this.validateStructure(data, schema);
        if (!structureValid.valid) {
            throw new Error(`Validation failed: ${structureValid.errors.join(', ')}`);
        }
        
        // Validaciones asíncronas (ej: unicidad, referencias)
        if (schema.asyncValidations) {
            for (const validation of schema.asyncValidations) {
                const isValid = await this.executeAsyncValidation(validation, data);
                if (!isValid.valid) {
                    throw new Error(`Async validation failed: ${isValid.error}`);
                }
            }
        }
        
        return true;
    }
    
    /**
     * Invalida cache relacionado de forma inteligente
     * @param {string} entityType - Tipo de entidad
     * @param {string|number} entityId - ID de la entidad
     * @returns {Promise<number>} Número de entradas invalidadas
     */
    async invalidateRelatedCache(entityType, entityId) {
        let invalidatedCount = 0;
        
        // Invalidar cache directo
        const directKey = `${entityType}:${entityId}`;
        if (this.cache.has(directKey)) {
            this.cache.delete(directKey);
            invalidatedCount++;
        }
        
        // Invalidar búsquedas relacionadas
        for (const [key] of this.cache) {
            if (key.startsWith(`search:${entityType}:`)) {
                this.cache.delete(key);
                invalidatedCount++;
            }
        }
        
        // Invalidar relaciones si están configuradas
        const relations = await this.getEntityRelations(entityType);
        for (const relation of relations) {
            const relatedKeys = Array.from(this.cache.keys()).filter(key => 
                key.includes(relation.entityType) && key.includes(entityId)
            );
            
            relatedKeys.forEach(key => {
                this.cache.delete(key);
                invalidatedCount++;
            });
        }
        
        return invalidatedCount;
    }
    
    // ===== MÉTODOS PRIVADOS =====
    
    /**
     * Ejecuta una request HTTP con configuración avanzada
     * @param {string} method - Método HTTP
     * @param {string} endpoint - Endpoint de la API
     * @param {Object} data - Datos a enviar
     * @param {Object} options - Opciones de la request
     * @returns {Promise<Object>} Respuesta de la API
     */
    async executeRequest(method, endpoint, data = null, options = {}) {
        const url = `${this.apiBaseUrl}${endpoint}`;
        const headers = { ...this.defaultHeaders, ...options.headers };
        
        const config = {
            method,
            headers,
            signal: options.signal // Para cancelación
        };
        
        if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
            config.body = JSON.stringify(data);
        }
        
        const response = await fetch(url, config);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            const error = new Error(errorData.message || `HTTP ${response.status}`);
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        
        return await response.json();
    }
    
    /**
     * Crea un delay asíncrono
     * @param {number} ms - Milisegundos de delay
     * @returns {Promise<void>}
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * Actualiza métricas de performance
     * @param {number} startTime - Tiempo de inicio
     * @param {boolean} success - Si la operación fue exitosa
     */
    updateMetrics(startTime, success) {
        const responseTime = Date.now() - startTime;
        this.metrics.totalRequests++;
        
        // Actualizar tiempo promedio de respuesta
        const totalRequests = this.metrics.totalRequests;
        const currentAvg = this.metrics.averageResponseTime;
        this.metrics.averageResponseTime = ((currentAvg * (totalRequests - 1)) + responseTime) / totalRequests;
        
        // Actualizar tasa de error
        if (!success) {
            this.metrics.errorRate = ((this.metrics.errorRate * (totalRequests - 1)) + 1) / totalRequests;
        } else {
            this.metrics.errorRate = (this.metrics.errorRate * (totalRequests - 1)) / totalRequests;
        }
    }
    
    /**
     * Mejora errores con contexto adicional
     * @param {Error} error - Error original
     * @param {string} method - Método donde ocurrió el error
     * @param {Object} context - Contexto adicional
     * @returns {Error} Error mejorado
     */
    enhanceError(error, method, context) {
        const enhancedError = new Error(`${method}: ${error.message}`);
        enhancedError.originalError = error;
        enhancedError.method = method;
        enhancedError.context = context;
        enhancedError.timestamp = new Date().toISOString();
        enhancedError.stack = error.stack;
        
        return enhancedError;
    }
    
    // ===== API PÚBLICA DE GESTIÓN =====
    
    /**
     * Obtiene métricas actuales del gestor
     * @returns {Object} Métricas detalladas
     */
    getMetrics() {
        return {
            ...this.metrics,
            cacheSize: this.cache.size,
            pendingRequests: this.pendingRequests.size,
            queueSize: this.requestQueue.length,
            cacheHitRate: this.metrics.totalRequests > 0 ? 
                (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * Limpia cache y reinicia métricas
     * @returns {Object} Estadísticas de limpieza
     */
    async cleanup() {
        const stats = {
            cacheCleared: this.cache.size,
            pendingCancelled: this.pendingRequests.size,
            queueCleared: this.requestQueue.length
        };
        
        this.cache.clear();
        this.pendingRequests.clear();
        this.requestQueue = [];
        
        // Reiniciar métricas
        this.metrics = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0,
            averageResponseTime: 0,
            errorRate: 0
        };
        
        return stats;
    }
}

// ===== EJEMPLO DE USO PRÁCTICO =====

// Crear instancia del gestor
const dataManager = new AdvancedDataManager({
    apiBaseUrl: 'https://jsonplaceholder.typicode.com',
    cacheTimeout: 300000, // 5 minutos
    maxRetries: 3,
    batchSize: 5
});

// Ejemplo 1: Operaciones CRUD básicas
async function ejemploCRUD() {
    try {
        console.log('=== Ejemplo CRUD ===');
        
        // Obtener usuario
        const user = await dataManager.getData('users', 1);
        console.log('Usuario obtenido:', user.name);
        
        // Crear nuevo post
        const newPost = await dataManager.createEntity('posts', {
            title: 'Mi nuevo post',
            body: 'Contenido del post',
            userId: 1
        });
        console.log('Post creado:', newPost.id);
        
        // Actualizar post
        const updatedPost = await dataManager.updateEntity('posts', newPost.id, {
            title: 'Post actualizado'
        });
        console.log('Post actualizado:', updatedPost.title);
        
    } catch (error) {
        console.error('Error en CRUD:', error.message);
    }
}

// Ejemplo 2: Procesamiento en lotes
async function ejemploLotes() {
    try {
        console.log('=== Ejemplo Lotes ===');
        
        const operations = [
            { type: 'get', entityType: 'users', id: 1 },
            { type: 'get', entityType: 'users', id: 2 },
            { type: 'get', entityType: 'users', id: 3 },
            { type: 'create', entityType: 'posts', data: { title: 'Post 1', body: 'Contenido 1', userId: 1 } },
            { type: 'create', entityType: 'posts', data: { title: 'Post 2', body: 'Contenido 2', userId: 2 } }
        ];
        
        const result = await dataManager.processBatch(operations, {
            batchSize: 3,
            onProgress: (progress) => {
                console.log(`Progreso: ${progress.completed}/${progress.total} (${progress.successCount} éxitos)`);
            }
        });
        
        console.log('Resultado del lote:', result.summary);
        
    } catch (error) {
        console.error('Error en lotes:', error.message);
    }
}

// Ejemplo 3: Búsqueda avanzada
async function ejemploBusqueda() {
    try {
        console.log('=== Ejemplo Búsqueda ===');
        
        const results = await dataManager.searchEntities('posts', {
            userId: 1
        }, {
            page: 1,
            limit: 5,
            sort: 'title',
            useCache: true
        });
        
        console.log(`Encontrados ${results.data.length} posts de ${results.pagination.total} total`);
        
    } catch (error) {
        console.error('Error en búsqueda:', error.message);
    }
}

// Ejecutar ejemplos
async function ejecutarEjemplos() {
    await ejemploCRUD();
    await ejemploLotes();
    await ejemploBusqueda();
    
    // Mostrar métricas finales
    console.log('=== Métricas Finales ===');
    console.log(dataManager.getMetrics());
}

ejecutarEjemplos();
```

## **🔍 EXPLICACIÓN EXHAUSTIVA DEL CÓDIGO**

### **Líneas 35-85: Método getData con Cache Inteligente**
```javascript
async getData(entityType, id, options = {}) {
    const startTime = Date.now();
    const cacheKey = `${entityType}:${id}`;
    
    if (!forceRefresh && this.cache.has(cacheKey)) {
        // Lógica de cache...
    }
}
```

Este método demuestra el **patrón Cache-Aside** implementado con async/await. La función maneja múltiples concerns de manera elegante: verificación de cache, deduplicación de requests (evitando el "thundering herd problem"), y métricas de performance. El uso de `await` hace que el código sea legible mientras mantiene la eficiencia asíncrona.

### **Líneas 120-180: Método createEntity con Validación Asíncrona**
```javascript
async createEntity(entityType, data, options = {}) {
    if (validateData) {
        await this.validateEntityData(entityType, data);
    }
    
    const result = await this.executeWithRetry(
        () => this.executeRequest('POST', `/${entityType}`, payload, options),
        this.maxRetries
    );
}
```

La creación de entidades implementa el **patrón Command** con validación asíncrona y retry automático. El método `executeWithRetry` encapsula la lógica de reintentos con backoff exponencial, mientras que la validación asíncrona permite verificar constrains complejos como unicidad o referencias a otras entidades.

### **Líneas 240-320: Procesamiento en Lotes**
```javascript
async processBatch(operations, options = {}) {
    for (let i = 0; i < operations.length; i += batchSize) {
        const batch = operations.slice(i, i + batchSize);
        const batchResults = await this.processSingleBatch(batch, options);
        
        if (options.onProgress) {
            await options.onProgress(progressData);
        }
    }
}
```

El procesamiento en lotes implementa el **patrón Batch Processing** con control de concurrencia y callbacks de progreso. Cada lote se procesa de manera concurrente usando `Promise.all`, pero los lotes se procesan secuencialmente para controlar la carga del sistema.

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de E-commerce con Inventario en Tiempo Real**
En una plataforma de e-commerce, este gestor permite manejar operaciones como verificación de stock, procesamiento de pedidos y actualización de inventario. El cache inteligente reduce la carga en la base de datos, mientras que el procesamiento en lotes permite actualizar múltiples productos simultáneamente durante promociones o cambios de precio masivos.

### **Caso 2: CRM con Sincronización de Datos Externos**
Para sistemas CRM que integran múltiples fuentes de datos (Salesforce, HubSpot, bases de datos internas), este gestor coordina la sincronización de contactos, leads y oportunidades. La validación asíncrona verifica la integridad de datos entre sistemas, mientras que el retry automático maneja fallos temporales de conectividad.

### **Caso 3: Sistema de Análisis de Datos en Tiempo Real**
En aplicaciones de business intelligence que procesan grandes volúmenes de datos, este gestor permite ejecutar consultas complejas, agregar resultados y mantener dashboards actualizados. El procesamiento en lotes optimiza las consultas a la base de datos, mientras que el cache reduce la latencia para métricas frecuentemente consultadas.

## **⚠️ ERRORES COMUNES**

### **Error 1: No Manejar Cancelación de Requests**
```javascript
// ❌ INCORRECTO - No permite cancelar requests
async getData(id) {
    return await fetch(`/api/data/${id}`);
}

// ✅ CORRECTO - Soporte para cancelación
async getData(id, options = {}) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);
    
    try {
        return await fetch(`/api/data/${id}`, {
            signal: options.signal || controller.signal
        });
    } finally {
        clearTimeout(timeoutId);
    }
}
```

### **Error 2: Memory Leaks en Cache**
```javascript
// ❌ INCORRECTO - Cache sin límites
class BadDataManager {
    async getData(id) {
        if (this.cache.has(id)) {
            return this.cache.get(id); // Cache crece indefinidamente
        }
        const data = await this.fetchData(id);
        this.cache.set(id, data);
        return data;
    }
}

// ✅ CORRECTO - Cache con TTL y límites
async getData(id) {
    const cached = this.cache.get(id);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
    }
    
    // Limpiar cache si excede límite
    if (this.cache.size > this.maxCacheSize) {
        this.evictOldestEntries();
    }
}
```

### **Error 3: No Manejar Errores de Red Correctamente**
```javascript
// ❌ INCORRECTO - No distingue tipos de error
async updateData(id, data) {
    try {
        return await this.api.update(id, data);
    } catch (error) {
        throw error; // Reintenta incluso errores 4xx
    }
}

// ✅ CORRECTO - Manejo inteligente de errores
async updateData(id, data) {
    try {
        return await this.executeWithRetry(async () => {
            const response = await this.api.update(id, data);
            return response;
        });
    } catch (error) {
        // No reintentar errores de cliente
        if (error.status >= 400 && error.status < 500) {
            throw error;
        }
        // Reintentar errores de servidor
        throw error;
    }
}
```

## **💡 RECOMENDACIONES PARA DOMINAR ASYNC/AWAIT EN OOP**

### **1. Diseña APIs Consistentes**
Mantén una interfaz consistente en todos tus métodos async. Usa convenciones claras para parámetros, opciones y valores de retorno. Considera implementar interfaces TypeScript para mayor claridad.

### **2. Implementa Observabilidad Completa**
Añade logging, métricas y tracing a todos tus métodos async. Usa herramientas como OpenTelemetry para rastrear requests distribuidos y identificar cuellos de botella.

### **3. Optimiza para Diferentes Patrones de Uso**
Diseña tu clase para manejar tanto operaciones individuales como en lote. Implementa estrategias de cache adaptativas que se ajusten según los patrones de acceso.

### **4. Maneja Recursos de Manera Responsable**
Implementa cleanup automático, límites de memoria y cancelación de operaciones. Usa WeakMap para referencias que no deben prevenir garbage collection.

### **5. Testa Exhaustivamente Escenarios Async**
Crea tests que cubran timeouts, retries, cancelaciones y condiciones de carrera. Usa herramientas como Jest con fake timers para probar comportamientos temporales.

## **🤔 PREGUNTAS SOCRÁTICAS PARA REFLEXIÓN**

- ¿Cómo diseñarías un sistema de prioridades para requests que permita ejecutar operaciones críticas antes que las de baja prioridad?
- ¿Qué estrategias implementarías para manejar operaciones que requieren transacciones distribuidas across múltiples servicios?
- ¿Cómo optimizarías el sistema para diferentes patrones de red (alta latencia, conexiones intermitentes, ancho de banda limitado)?
- ¿De qué manera integrarías este gestor con sistemas de state management como Redux o Vuex para mantener consistencia en el frontend?

## **📊 VISUALIZACIONES ANATÓMICAS**

### **Anatomía de Métodos Async en Clases**

![Anatomía Async Methods](SVG/async_methods_anatomy.svg)

### **Flujo de Ejecución con Cache y Retry**

![Flujo Async](SVG/async_execution_flow.svg)

### **Gestión de Estado Asíncrono**

![Estado Asíncrono](SVG/async_state_management.svg)

### **Mapa Mental: Async/Await en OOP**

![Mapa Mental Async](SVG/async_await_mindmap.svg)
