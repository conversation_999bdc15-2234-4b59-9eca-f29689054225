# **52.4 - MANEJO DE ERRORES ASÍNCRONOS**

## **📖 INTRODUCCIÓN**

El manejo de errores asíncronos representa uno de los aspectos más críticos y complejos de la programación moderna, donde la diferencia entre un sistema robusto y uno frágil radica en cómo se anticipan, capturan, procesan y recuperan de los fallos inevitables que ocurren en operaciones distribuidas y asíncronas. En el contexto de la programación orientada a objetos con JavaScript, el manejo de errores asíncronos va mucho más allá de simples bloques try/catch; requiere una arquitectura sofisticada que incluya estrategias de retry inteligentes, circuit breakers para prevenir cascadas de fallos, logging estructurado para debugging efectivo, y mecanismos de recovery que permitan al sistema continuar operando incluso cuando componentes individuales fallan. En aplicaciones empresariales modernas, donde los usuarios esperan disponibilidad 24/7 y los sistemas deben manejar cargas variables y servicios externos impredecibles, dominar el manejo de errores asíncronos es la diferencia entre una aplicación que inspira confianza y una que genera frustración. Esta maestría incluye entender cuándo fallar rápido versus cuándo persistir, cómo propagar errores de manera que preserven contexto útil para debugging, y cómo implementar estrategias de fallback que mantengan la funcionalidad core incluso cuando servicios auxiliares están indisponibles. El objetivo no es eliminar errores—algo imposible en sistemas distribuidos—sino crear sistemas que fallen de manera predecible, se recuperen elegantemente, y proporcionen información valiosa para mejorar continuamente la resilencia del sistema.

## **💻 CÓDIGO DE EJEMPLO**

```javascript
// ===== SISTEMA AVANZADO DE MANEJO DE ERRORES ASÍNCRONOS =====

class AdvancedErrorHandler {
    constructor(options = {}) {
        // Configuración del manejador
        this.retryStrategies = new Map();
        this.errorClassifiers = [];
        this.fallbackStrategies = new Map();
        this.errorListeners = new Map();
        
        // Configuración por defecto
        this.defaultRetryConfig = {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffMultiplier: 2,
            jitter: true
        };
        
        // Métricas de errores
        this.errorMetrics = {
            totalErrors: 0,
            errorsByType: new Map(),
            errorsByOperation: new Map(),
            retrySuccessRate: 0,
            averageRecoveryTime: 0
        };
        
        // Contexto global para debugging
        this.globalContext = {
            sessionId: this.generateSessionId(),
            startTime: Date.now(),
            environment: options.environment || 'development'
        };
        
        // Configurar manejadores por defecto
        this.setupDefaultHandlers();
    }
    
    // ===== CLASIFICACIÓN Y CATEGORIZACIÓN DE ERRORES =====
    
    /**
     * Registra un clasificador de errores
     * @param {Function} classifier - Función que clasifica errores
     * @param {number} priority - Prioridad del clasificador
     */
    registerErrorClassifier(classifier, priority = 0) {
        this.errorClassifiers.push({ classifier, priority });
        this.errorClassifiers.sort((a, b) => b.priority - a.priority);
    }
    
    /**
     * Clasifica un error según los clasificadores registrados
     * @param {Error} error - Error a clasificar
     * @param {Object} context - Contexto del error
     * @returns {Object} Clasificación del error
     */
    classifyError(error, context = {}) {
        const classification = {
            type: 'UNKNOWN',
            severity: 'MEDIUM',
            retryable: false,
            temporary: false,
            userFacing: false,
            category: 'GENERAL'
        };
        
        // Aplicar clasificadores en orden de prioridad
        for (const { classifier } of this.errorClassifiers) {
            try {
                const result = classifier(error, context);
                if (result) {
                    Object.assign(classification, result);
                    break;
                }
            } catch (classifierError) {
                console.warn('Error in classifier:', classifierError);
            }
        }
        
        return classification;
    }
    
    // ===== ESTRATEGIAS DE RETRY AVANZADAS =====
    
    /**
     * Registra una estrategia de retry personalizada
     * @param {string} name - Nombre de la estrategia
     * @param {Object} config - Configuración de la estrategia
     */
    registerRetryStrategy(name, config) {
        this.retryStrategies.set(name, {
            ...this.defaultRetryConfig,
            ...config
        });
    }
    
    /**
     * Ejecuta una operación con retry inteligente
     * @param {Function} operation - Operación asíncrona a ejecutar
     * @param {Object} options - Opciones de retry
     * @returns {Promise} Resultado de la operación
     */
    async executeWithRetry(operation, options = {}) {
        const operationId = this.generateOperationId();
        const strategy = options.strategy || 'default';
        const retryConfig = this.retryStrategies.get(strategy) || this.defaultRetryConfig;
        const context = {
            operationId,
            operation: options.operationName || 'anonymous',
            startTime: Date.now(),
            ...options.context
        };
        
        let lastError;
        let attempt = 0;
        const maxAttempts = options.maxAttempts || retryConfig.maxAttempts;
        
        while (attempt < maxAttempts) {
            attempt++;
            
            try {
                const startTime = Date.now();
                const result = await this.executeWithTimeout(
                    operation,
                    options.timeout || 30000,
                    context
                );
                
                // Registrar éxito si hubo reintentos
                if (attempt > 1) {
                    this.recordRetrySuccess(context, attempt, Date.now() - context.startTime);
                }
                
                return result;
                
            } catch (error) {
                lastError = this.enhanceError(error, context, attempt);
                const classification = this.classifyError(lastError, context);
                
                // Verificar si el error es retryable
                if (!classification.retryable || attempt >= maxAttempts) {
                    break;
                }
                
                // Calcular delay para próximo intento
                const delay = this.calculateRetryDelay(
                    attempt,
                    retryConfig,
                    classification
                );
                
                // Notificar intento de retry
                await this.notifyRetryAttempt(lastError, attempt, delay, context);
                
                // Esperar antes del próximo intento
                await this.delay(delay);
            }
        }
        
        // Todos los intentos fallaron
        this.recordRetryFailure(context, attempt, lastError);
        throw lastError;
    }
    
    /**
     * Calcula el delay para el próximo retry
     * @param {number} attempt - Número de intento actual
     * @param {Object} config - Configuración de retry
     * @param {Object} classification - Clasificación del error
     * @returns {number} Delay en milisegundos
     */
    calculateRetryDelay(attempt, config, classification) {
        let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
        
        // Aplicar límite máximo
        delay = Math.min(delay, config.maxDelay);
        
        // Ajustar según tipo de error
        if (classification.temporary) {
            delay *= 0.5; // Reducir delay para errores temporales
        }
        
        // Añadir jitter para evitar thundering herd
        if (config.jitter) {
            delay += Math.random() * delay * 0.1;
        }
        
        return Math.floor(delay);
    }
    
    // ===== MANEJO DE TIMEOUTS =====
    
    /**
     * Ejecuta operación con timeout configurable
     * @param {Function} operation - Operación a ejecutar
     * @param {number} timeout - Timeout en milisegundos
     * @param {Object} context - Contexto de la operación
     * @returns {Promise} Resultado de la operación
     */
    async executeWithTimeout(operation, timeout, context = {}) {
        let timeoutId;
        
        const timeoutPromise = new Promise((_, reject) => {
            timeoutId = setTimeout(() => {
                const error = new Error(`Operation timeout after ${timeout}ms`);
                error.code = 'TIMEOUT';
                error.timeout = timeout;
                error.context = context;
                reject(error);
            }, timeout);
        });
        
        try {
            const result = await Promise.race([
                operation(),
                timeoutPromise
            ]);
            
            clearTimeout(timeoutId);
            return result;
            
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }
    
    // ===== ESTRATEGIAS DE FALLBACK =====
    
    /**
     * Registra una estrategia de fallback
     * @param {string} operation - Nombre de la operación
     * @param {Function} fallbackFn - Función de fallback
     * @param {Object} options - Opciones del fallback
     */
    registerFallback(operation, fallbackFn, options = {}) {
        this.fallbackStrategies.set(operation, {
            fallback: fallbackFn,
            conditions: options.conditions || [],
            priority: options.priority || 0,
            timeout: options.timeout || 10000
        });
    }
    
    /**
     * Ejecuta operación con fallback automático
     * @param {string} operationName - Nombre de la operación
     * @param {Function} primaryOperation - Operación principal
     * @param {*} params - Parámetros de la operación
     * @param {Object} options - Opciones de ejecución
     * @returns {Promise} Resultado de la operación o fallback
     */
    async executeWithFallback(operationName, primaryOperation, params, options = {}) {
        const context = {
            operation: operationName,
            startTime: Date.now(),
            ...options.context
        };
        
        try {
            // Intentar operación principal
            return await this.executeWithRetry(
                () => primaryOperation(params),
                {
                    ...options,
                    operationName,
                    context
                }
            );
            
        } catch (primaryError) {
            const classification = this.classifyError(primaryError, context);
            
            // Verificar si hay fallback disponible
            const fallbackStrategy = this.fallbackStrategies.get(operationName);
            
            if (fallbackStrategy && this.shouldUseFallback(classification, fallbackStrategy)) {
                try {
                    console.warn(`Primary operation failed, using fallback for ${operationName}:`, primaryError.message);
                    
                    const fallbackResult = await this.executeWithTimeout(
                        () => fallbackStrategy.fallback(params, primaryError, context),
                        fallbackStrategy.timeout,
                        context
                    );
                    
                    // Marcar como resultado de fallback
                    return {
                        ...fallbackResult,
                        _fallback: true,
                        _primaryError: primaryError.message
                    };
                    
                } catch (fallbackError) {
                    // Fallback también falló
                    const combinedError = new Error(
                        `Both primary and fallback operations failed. Primary: ${primaryError.message}, Fallback: ${fallbackError.message}`
                    );
                    combinedError.primaryError = primaryError;
                    combinedError.fallbackError = fallbackError;
                    throw combinedError;
                }
            }
            
            // No hay fallback o no se debe usar
            throw primaryError;
        }
    }
    
    /**
     * Determina si se debe usar fallback
     * @param {Object} classification - Clasificación del error
     * @param {Object} strategy - Estrategia de fallback
     * @returns {boolean} True si se debe usar fallback
     */
    shouldUseFallback(classification, strategy) {
        // Verificar condiciones específicas
        if (strategy.conditions.length > 0) {
            return strategy.conditions.some(condition => condition(classification));
        }
        
        // Usar fallback para errores no temporales o críticos
        return !classification.temporary || classification.severity === 'HIGH';
    }
    
    // ===== LOGGING Y OBSERVABILIDAD =====
    
    /**
     * Mejora un error con contexto adicional
     * @param {Error} error - Error original
     * @param {Object} context - Contexto de la operación
     * @param {number} attempt - Número de intento
     * @returns {Error} Error mejorado
     */
    enhanceError(error, context, attempt = 1) {
        const enhancedError = new Error(error.message);
        
        // Preservar propiedades originales
        Object.assign(enhancedError, error);
        
        // Añadir contexto adicional
        enhancedError.context = {
            ...this.globalContext,
            ...context,
            attempt,
            timestamp: new Date().toISOString(),
            errorId: this.generateErrorId()
        };
        
        // Preservar stack trace
        enhancedError.stack = error.stack;
        enhancedError.originalError = error;
        
        return enhancedError;
    }
    
    /**
     * Registra métricas de error
     * @param {Error} error - Error ocurrido
     * @param {Object} context - Contexto del error
     */
    recordError(error, context = {}) {
        this.errorMetrics.totalErrors++;
        
        const classification = this.classifyError(error, context);
        
        // Contar por tipo
        const errorType = classification.type;
        this.errorMetrics.errorsByType.set(
            errorType,
            (this.errorMetrics.errorsByType.get(errorType) || 0) + 1
        );
        
        // Contar por operación
        const operation = context.operation || 'unknown';
        this.errorMetrics.errorsByOperation.set(
            operation,
            (this.errorMetrics.errorsByOperation.get(operation) || 0) + 1
        );
        
        // Notificar a listeners
        this.notifyErrorListeners('error', {
            error,
            classification,
            context,
            metrics: this.getErrorMetrics()
        });
    }
    
    /**
     * Registra éxito de retry
     * @param {Object} context - Contexto de la operación
     * @param {number} attempts - Número de intentos
     * @param {number} totalTime - Tiempo total de recuperación
     */
    recordRetrySuccess(context, attempts, totalTime) {
        // Actualizar métricas de retry
        const currentSuccessRate = this.errorMetrics.retrySuccessRate;
        this.errorMetrics.retrySuccessRate = (currentSuccessRate + 1) / 2; // Promedio móvil simple
        
        // Actualizar tiempo promedio de recuperación
        const currentAvgTime = this.errorMetrics.averageRecoveryTime;
        this.errorMetrics.averageRecoveryTime = (currentAvgTime + totalTime) / 2;
        
        this.notifyErrorListeners('retrySuccess', {
            context,
            attempts,
            totalTime,
            metrics: this.getErrorMetrics()
        });
    }
    
    /**
     * Registra fallo de retry
     * @param {Object} context - Contexto de la operación
     * @param {number} attempts - Número de intentos
     * @param {Error} finalError - Error final
     */
    recordRetryFailure(context, attempts, finalError) {
        this.recordError(finalError, context);
        
        this.notifyErrorListeners('retryFailure', {
            context,
            attempts,
            finalError,
            metrics: this.getErrorMetrics()
        });
    }
    
    // ===== SISTEMA DE EVENTOS =====
    
    /**
     * Suscribe a eventos de error
     * @param {string} event - Tipo de evento
     * @param {Function} listener - Listener del evento
     */
    onError(event, listener) {
        if (!this.errorListeners.has(event)) {
            this.errorListeners.set(event, []);
        }
        this.errorListeners.get(event).push(listener);
    }
    
    /**
     * Notifica a listeners de eventos
     * @param {string} event - Tipo de evento
     * @param {Object} data - Datos del evento
     */
    notifyErrorListeners(event, data) {
        const listeners = this.errorListeners.get(event) || [];
        listeners.forEach(listener => {
            try {
                listener(data);
            } catch (listenerError) {
                console.error(`Error in error listener for ${event}:`, listenerError);
            }
        });
    }
    
    /**
     * Notifica intento de retry
     * @param {Error} error - Error que causó el retry
     * @param {number} attempt - Número de intento
     * @param {number} delay - Delay antes del próximo intento
     * @param {Object} context - Contexto de la operación
     */
    async notifyRetryAttempt(error, attempt, delay, context) {
        this.notifyErrorListeners('retryAttempt', {
            error,
            attempt,
            delay,
            context
        });
    }
    
    // ===== CONFIGURACIÓN POR DEFECTO =====
    
    /**
     * Configura manejadores por defecto
     */
    setupDefaultHandlers() {
        // Clasificador de errores de red
        this.registerErrorClassifier((error, context) => {
            if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
                return {
                    type: 'NETWORK_ERROR',
                    severity: 'HIGH',
                    retryable: true,
                    temporary: true,
                    category: 'CONNECTIVITY'
                };
            }
        }, 10);
        
        // Clasificador de timeouts
        this.registerErrorClassifier((error, context) => {
            if (error.code === 'TIMEOUT') {
                return {
                    type: 'TIMEOUT_ERROR',
                    severity: 'MEDIUM',
                    retryable: true,
                    temporary: true,
                    category: 'PERFORMANCE'
                };
            }
        }, 9);
        
        // Clasificador de errores HTTP
        this.registerErrorClassifier((error, context) => {
            if (error.status) {
                const status = error.status;
                if (status >= 500) {
                    return {
                        type: 'SERVER_ERROR',
                        severity: 'HIGH',
                        retryable: true,
                        temporary: true,
                        category: 'SERVER'
                    };
                } else if (status >= 400) {
                    return {
                        type: 'CLIENT_ERROR',
                        severity: 'MEDIUM',
                        retryable: false,
                        temporary: false,
                        category: 'CLIENT'
                    };
                }
            }
        }, 8);
        
        // Estrategia de retry por defecto
        this.registerRetryStrategy('default', this.defaultRetryConfig);
        
        // Estrategia de retry agresiva
        this.registerRetryStrategy('aggressive', {
            maxAttempts: 5,
            baseDelay: 500,
            maxDelay: 10000,
            backoffMultiplier: 1.5,
            jitter: true
        });
        
        // Estrategia de retry conservadora
        this.registerRetryStrategy('conservative', {
            maxAttempts: 2,
            baseDelay: 2000,
            maxDelay: 60000,
            backoffMultiplier: 3,
            jitter: false
        });
    }
    
    // ===== UTILIDADES =====
    
    /**
     * Obtiene métricas actuales de errores
     * @returns {Object} Métricas de errores
     */
    getErrorMetrics() {
        return {
            ...this.errorMetrics,
            errorsByType: Object.fromEntries(this.errorMetrics.errorsByType),
            errorsByOperation: Object.fromEntries(this.errorMetrics.errorsByOperation),
            uptime: Date.now() - this.globalContext.startTime
        };
    }
    
    /**
     * Reinicia métricas de errores
     */
    resetMetrics() {
        this.errorMetrics = {
            totalErrors: 0,
            errorsByType: new Map(),
            errorsByOperation: new Map(),
            retrySuccessRate: 0,
            averageRecoveryTime: 0
        };
    }
    
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    generateOperationId() {
        return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    generateErrorId() {
        return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

// ===== EJEMPLO DE USO PRÁCTICO =====

// Crear instancia del manejador de errores
const errorHandler = new AdvancedErrorHandler({
    environment: 'production'
});

// Configurar listeners de eventos
errorHandler.onError('error', (data) => {
    console.log(`❌ Error ${data.classification.type}:`, data.error.message);
});

errorHandler.onError('retryAttempt', (data) => {
    console.log(`🔄 Retry attempt ${data.attempt} in ${data.delay}ms for:`, data.context.operation);
});

errorHandler.onError('retrySuccess', (data) => {
    console.log(`✅ Retry successful after ${data.attempts} attempts in ${data.totalTime}ms`);
});

// Registrar fallback para operación crítica
errorHandler.registerFallback('fetchUserData', async (params, primaryError, context) => {
    console.log('Using cached user data as fallback');
    return {
        id: params.userId,
        name: 'Cached User',
        email: '<EMAIL>',
        _cached: true
    };
});

// Ejemplo 1: Operación con retry automático
async function ejemploRetry() {
    try {
        const result = await errorHandler.executeWithRetry(async () => {
            // Simular operación que puede fallar
            if (Math.random() > 0.6) {
                const error = new Error('Service temporarily unavailable');
                error.status = 503;
                throw error;
            }
            return { data: 'Success!' };
        }, {
            strategy: 'aggressive',
            operationName: 'fetchData',
            timeout: 5000
        });
        
        console.log('Resultado:', result);
    } catch (error) {
        console.error('Operación falló definitivamente:', error.message);
    }
}

// Ejemplo 2: Operación con fallback
async function ejemploFallback() {
    try {
        const result = await errorHandler.executeWithFallback(
            'fetchUserData',
            async (params) => {
                // Simular fallo de API
                throw new Error('API service down');
            },
            { userId: 123 },
            { strategy: 'conservative' }
        );
        
        console.log('Usuario obtenido:', result);
    } catch (error) {
        console.error('Tanto operación principal como fallback fallaron:', error.message);
    }
}

// Ejemplo 3: Operación con timeout
async function ejemploTimeout() {
    try {
        const result = await errorHandler.executeWithTimeout(async () => {
            // Simular operación lenta
            await new Promise(resolve => setTimeout(resolve, 10000));
            return 'Completed';
        }, 3000);
        
        console.log('Resultado:', result);
    } catch (error) {
        console.error('Operación timeout:', error.message);
    }
}

// Ejecutar ejemplos
async function ejecutarEjemplos() {
    console.log('=== Ejemplo Retry ===');
    await ejemploRetry();
    
    console.log('\n=== Ejemplo Fallback ===');
    await ejemploFallback();
    
    console.log('\n=== Ejemplo Timeout ===');
    await ejemploTimeout();
    
    // Mostrar métricas finales
    console.log('\n=== Métricas de Errores ===');
    console.log(errorHandler.getErrorMetrics());
}

ejecutarEjemplos();
```

## **🔍 EXPLICACIÓN EXHAUSTIVA DEL CÓDIGO**

### **Clasificación Inteligente de Errores (Líneas 35-75)**
El sistema de clasificación permite categorizar errores automáticamente según múltiples criterios: tipo, severidad, si es retryable, si es temporal, etc. Esto permite tomar decisiones inteligentes sobre cómo manejar cada error específico, aplicando diferentes estrategias según el contexto.

### **Estrategias de Retry Configurables (Líneas 85-180)**
El sistema implementa múltiples estrategias de retry (default, aggressive, conservative) con backoff exponencial, jitter para evitar thundering herd, y clasificación de errores para determinar si vale la pena reintentar. Cada estrategia puede ajustarse según las características específicas de la operación.

### **Sistema de Fallback Robusto (Líneas 220-320)**
Los fallbacks proporcionan funcionalidad degradada cuando las operaciones principales fallan. El sistema evalúa automáticamente si usar fallback según la clasificación del error y las condiciones configuradas, permitiendo que la aplicación continúe funcionando incluso con servicios externos caídos.

### **Observabilidad y Métricas (Líneas 350-450)**
El sistema registra métricas detalladas sobre errores, tasas de éxito de retry, tiempos de recuperación, y patrones de fallo. Esto es crucial para monitoring, alerting, y optimización continua de las estrategias de manejo de errores.

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: API Gateway con Múltiples Servicios Backend**
En un API gateway que coordina múltiples microservicios, este sistema permite manejar fallos de servicios individuales con estrategias específicas: retry agresivo para servicios críticos, fallbacks a cache para datos no críticos, y circuit breakers para prevenir cascadas de fallos.

### **Caso 2: Sistema de Pagos con Alta Disponibilidad**
Para sistemas de pagos donde la disponibilidad es crítica, el manejo de errores debe ser extremadamente robusto: retry conservador para evitar doble cobro, fallbacks a procesadores alternativos, logging detallado para auditoría, y recovery automático de estados inconsistentes.

### **Caso 3: Plataforma de Analytics en Tiempo Real**
En sistemas de analytics que procesan grandes volúmenes de datos, el manejo de errores debe balancear throughput y accuracy: retry rápido para errores temporales, fallbacks a estimaciones cuando datos exactos no están disponibles, y degradación graceful durante picos de carga.

## **💡 RECOMENDACIONES PARA DOMINAR MANEJO DE ERRORES ASÍNCRONOS**

### **1. Diseña una Taxonomía de Errores Clara**
Crea una clasificación consistente de errores que permita tomar decisiones automáticas sobre retry, fallback y escalation. Incluye dimensiones como severidad, temporalidad, origen y impacto en el usuario.

### **2. Implementa Observabilidad desde el Diseño**
Cada error debe generar métricas, logs estructurados y traces que permitan debugging efectivo. Usa correlation IDs para rastrear errores a través de sistemas distribuidos.

### **3. Optimiza Estrategias Basado en Datos Reales**
Analiza patrones de errores en producción para ajustar timeouts, retry counts y backoff strategies. Lo que funciona en desarrollo puede no ser óptimo en producción.

### **4. Balancea Resilencia con Performance**
Más retries no siempre es mejor. Encuentra el balance entre resilencia y latencia que proporcione la mejor experiencia de usuario para tu caso específico.

### **5. Testa Escenarios de Fallo Sistemáticamente**
Implementa chaos engineering y fault injection para validar que tu manejo de errores funciona correctamente bajo condiciones adversas reales.

## **📊 VISUALIZACIONES ANATÓMICAS**

### **Anatomía del Manejo de Errores**

![Manejo de Errores](SVG/error_handling_anatomy.svg)

### **Flujo de Retry y Recovery**

![Retry Flow](SVG/retry_recovery_flow.svg)

### **Estrategias de Fallback**

![Fallback Strategies](SVG/fallback_strategies.svg)

### **Mapa Mental: Manejo de Errores Asíncronos**

![Mapa Mental Errores](SVG/error_handling_mindmap.svg)
