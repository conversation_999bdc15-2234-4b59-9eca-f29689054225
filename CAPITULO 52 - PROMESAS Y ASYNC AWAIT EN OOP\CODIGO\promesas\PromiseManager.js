/**
 * Sistema Avanzado de Gestión de Promesas
 * 
 * Este módulo implementa un gestor completo de promesas con características empresariales:
 * - Control de concurrencia
 * - Timeout y retry automático
 * - Métricas y observabilidad
 * - Cleanup automático de memoria
 * - Sistema de eventos para monitoreo
 * 
 * <AUTHOR> Técnico Superior
 * @version 1.0.0
 */

class AdvancedPromiseManager {
    constructor(options = {}) {
        // Configuración del gestor
        this.maxConcurrency = options.maxConcurrency || 5;
        this.defaultTimeout = options.defaultTimeout || 30000;
        this.retryAttempts = options.retryAttempts || 3;
        this.retryDelay = options.retryDelay || 1000;
        
        // Estado interno
        this.activePromises = new Map();
        this.promiseQueue = [];
        this.completedPromises = new Map();
        this.failedPromises = new Map();
        
        // Métricas y estadísticas
        this.metrics = {
            totalPromises: 0,
            successfulPromises: 0,
            failedPromises: 0,
            averageExecutionTime: 0,
            concurrentPeak: 0
        };
        
        // Event listeners para monitoreo
        this.eventListeners = new Map();
        
        // Cleanup automático
        this.cleanupInterval = setInterval(() => this.cleanup(), 60000);
    }
    
    /**
     * Crea una promesa con configuración avanzada
     * @param {Function} executor - Función ejecutora de la promesa
     * @param {Object} options - Opciones de configuración
     * @returns {Promise} Promesa configurada con timeout y retry
     */
    createPromise(executor, options = {}) {
        const promiseId = this.generatePromiseId();
        const config = {
            id: promiseId,
            timeout: options.timeout || this.defaultTimeout,
            retryAttempts: options.retryAttempts || this.retryAttempts,
            retryDelay: options.retryDelay || this.retryDelay,
            priority: options.priority || 'normal',
            tags: options.tags || [],
            createdAt: Date.now()
        };
        
        // Crear promesa con timeout automático
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Promise ${promiseId} timed out after ${config.timeout}ms`));
            }, config.timeout);
        });
        
        // Crear promesa principal con retry automático
        const mainPromise = this.createRetryablePromise(executor, config);
        
        // Combinar con timeout usando Promise.race
        const finalPromise = Promise.race([mainPromise, timeoutPromise])
            .then(result => {
                this.handlePromiseSuccess(promiseId, result);
                return result;
            })
            .catch(error => {
                this.handlePromiseFailure(promiseId, error);
                throw error;
            });
        
        // Registrar promesa activa
        this.activePromises.set(promiseId, {
            promise: finalPromise,
            config: config,
            startTime: Date.now()
        });
        
        this.metrics.totalPromises++;
        this.updateConcurrencyMetrics();
        this.emit('promiseCreated', { id: promiseId, config });
        
        return finalPromise;
    }
    
    /**
     * Crea una promesa con capacidad de retry automático
     * @param {Function} executor - Función ejecutora
     * @param {Object} config - Configuración de la promesa
     * @returns {Promise} Promesa con retry
     */
    createRetryablePromise(executor, config) {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            
            const attemptExecution = () => {
                attempts++;
                
                try {
                    // Ejecutar la función con manejo de errores
                    const result = executor(resolve, reject);
                    
                    // Si el executor retorna una promesa, manejarla
                    if (result && typeof result.then === 'function') {
                        result
                            .then(resolve)
                            .catch(handleError);
                    }
                } catch (error) {
                    handleError(error);
                }
            };
            
            const handleError = (error) => {
                if (attempts < config.retryAttempts) {
                    this.emit('promiseRetry', {
                        id: config.id,
                        attempt: attempts,
                        error: error.message
                    });
                    
                    // Retry con delay exponencial
                    const delay = config.retryDelay * Math.pow(2, attempts - 1);
                    setTimeout(attemptExecution, delay);
                } else {
                    reject(error);
                }
            };
            
            // Iniciar primera ejecución
            attemptExecution();
        });
    }
    
    /**
     * Ejecuta promesas con control de concurrencia
     * @param {Array} promiseFactories - Array de funciones que retornan promesas
     * @param {Object} options - Opciones de ejecución
     * @returns {Promise<Array>} Resultados de todas las promesas
     */
    async executeConcurrent(promiseFactories, options = {}) {
        const concurrency = options.concurrency || this.maxConcurrency;
        const failFast = options.failFast !== false;
        const results = [];
        const errors = [];
        
        // Dividir en lotes según la concurrencia
        for (let i = 0; i < promiseFactories.length; i += concurrency) {
            const batch = promiseFactories.slice(i, i + concurrency);
            
            try {
                const batchPromises = batch.map((factory, index) => 
                    this.createPromise(factory, {
                        ...options,
                        tags: [...(options.tags || []), `batch-${Math.floor(i / concurrency)}`]
                    }).catch(error => {
                        errors.push({ index: i + index, error });
                        if (failFast) throw error;
                        return null; // Placeholder para mantener índices
                    })
                );
                
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
                
            } catch (error) {
                if (failFast) {
                    throw new Error(`Batch execution failed: ${error.message}`);
                }
            }
        }
        
        return {
            results,
            errors,
            successCount: results.filter(r => r !== null).length,
            errorCount: errors.length
        };
    }
    
    /**
     * Ejecuta promesas en secuencia con control de flujo
     * @param {Array} promiseFactories - Array de funciones que retornan promesas
     * @param {Object} options - Opciones de ejecución
     * @returns {Promise<Array>} Resultados en orden secuencial
     */
    async executeSequential(promiseFactories, options = {}) {
        const results = [];
        const stopOnError = options.stopOnError !== false;
        
        for (let i = 0; i < promiseFactories.length; i++) {
            try {
                const result = await this.createPromise(promiseFactories[i], {
                    ...options,
                    tags: [...(options.tags || []), `sequence-${i}`]
                });
                
                results.push(result);
                
                // Callback de progreso si está definido
                if (options.onProgress) {
                    options.onProgress({
                        completed: i + 1,
                        total: promiseFactories.length,
                        result
                    });
                }
                
            } catch (error) {
                if (stopOnError) {
                    throw new Error(`Sequential execution stopped at index ${i}: ${error.message}`);
                }
                results.push(null);
            }
        }
        
        return results;
    }
    
    /**
     * Convierte callback-style functions a promesas
     * @param {Function} fn - Función con callback
     * @param {Object} context - Contexto de ejecución
     * @returns {Function} Función promisificada
     */
    promisify(fn, context = null) {
        return (...args) => {
            return this.createPromise((resolve, reject) => {
                const callback = (error, result) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve(result);
                    }
                };
                
                try {
                    fn.call(context, ...args, callback);
                } catch (error) {
                    reject(error);
                }
            });
        };
    }
    
    /**
     * Crea una promesa que se resuelve después de un delay
     * @param {number} ms - Milisegundos de delay
     * @param {*} value - Valor a retornar
     * @returns {Promise} Promesa con delay
     */
    delay(ms, value = undefined) {
        return this.createPromise((resolve) => {
            setTimeout(() => resolve(value), ms);
        }, { timeout: ms + 1000 });
    }
    
    /**
     * Implementa timeout para cualquier promesa
     * @param {Promise} promise - Promesa a la cual aplicar timeout
     * @param {number} ms - Milisegundos de timeout
     * @returns {Promise} Promesa con timeout
     */
    timeout(promise, ms) {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Operation timed out after ${ms}ms`));
            }, ms);
        });
        
        return Promise.race([promise, timeoutPromise]);
    }
    
    // ===== MANEJO DE EVENTOS Y MÉTRICAS =====
    
    /**
     * Registra un event listener
     * @param {string} event - Nombre del evento
     * @param {Function} listener - Función listener
     */
    on(event, listener) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(listener);
    }
    
    /**
     * Emite un evento
     * @param {string} event - Nombre del evento
     * @param {*} data - Datos del evento
     */
    emit(event, data) {
        const listeners = this.eventListeners.get(event) || [];
        listeners.forEach(listener => {
            try {
                listener(data);
            } catch (error) {
                console.error(`Error in event listener for ${event}:`, error);
            }
        });
    }
    
    /**
     * Maneja el éxito de una promesa
     * @param {string} promiseId - ID de la promesa
     * @param {*} result - Resultado de la promesa
     */
    handlePromiseSuccess(promiseId, result) {
        const promiseData = this.activePromises.get(promiseId);
        if (promiseData) {
            const executionTime = Date.now() - promiseData.startTime;
            
            this.completedPromises.set(promiseId, {
                ...promiseData,
                result,
                executionTime,
                completedAt: Date.now()
            });
            
            this.activePromises.delete(promiseId);
            this.metrics.successfulPromises++;
            this.updateAverageExecutionTime(executionTime);
            
            this.emit('promiseResolved', {
                id: promiseId,
                result,
                executionTime
            });
        }
    }
    
    /**
     * Maneja el fallo de una promesa
     * @param {string} promiseId - ID de la promesa
     * @param {Error} error - Error de la promesa
     */
    handlePromiseFailure(promiseId, error) {
        const promiseData = this.activePromises.get(promiseId);
        if (promiseData) {
            const executionTime = Date.now() - promiseData.startTime;
            
            this.failedPromises.set(promiseId, {
                ...promiseData,
                error,
                executionTime,
                failedAt: Date.now()
            });
            
            this.activePromises.delete(promiseId);
            this.metrics.failedPromises++;
            
            this.emit('promiseRejected', {
                id: promiseId,
                error: error.message,
                executionTime
            });
        }
    }
    
    // ===== MÉTODOS PRIVADOS =====
    
    generatePromiseId() {
        return `promise_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    updateConcurrencyMetrics() {
        const currentConcurrency = this.activePromises.size;
        if (currentConcurrency > this.metrics.concurrentPeak) {
            this.metrics.concurrentPeak = currentConcurrency;
        }
    }
    
    updateAverageExecutionTime(newTime) {
        const total = this.metrics.successfulPromises;
        const currentAvg = this.metrics.averageExecutionTime;
        this.metrics.averageExecutionTime = ((currentAvg * (total - 1)) + newTime) / total;
    }
    
    cleanup() {
        const now = Date.now();
        const maxAge = 300000; // 5 minutos
        
        // Limpiar promesas completadas antiguas
        for (const [id, data] of this.completedPromises) {
            if (now - data.completedAt > maxAge) {
                this.completedPromises.delete(id);
            }
        }
        
        // Limpiar promesas fallidas antiguas
        for (const [id, data] of this.failedPromises) {
            if (now - data.failedAt > maxAge) {
                this.failedPromises.delete(id);
            }
        }
    }
    
    /**
     * Obtiene métricas actuales del gestor
     * @returns {Object} Métricas detalladas
     */
    getMetrics() {
        return {
            ...this.metrics,
            activePromises: this.activePromises.size,
            completedPromises: this.completedPromises.size,
            failedPromises: this.failedPromises.size,
            successRate: this.metrics.totalPromises > 0 ? 
                (this.metrics.successfulPromises / this.metrics.totalPromises * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * Obtiene el estado de todas las promesas activas
     * @returns {Array} Array con información de promesas activas
     */
    getActivePromises() {
        return Array.from(this.activePromises.entries()).map(([id, data]) => ({
            id,
            config: data.config,
            runningTime: Date.now() - data.startTime,
            estimatedCompletion: data.startTime + data.config.timeout
        }));
    }
    
    /**
     * Cancela todas las promesas activas
     * @returns {number} Número de promesas canceladas
     */
    cancelAllPromises() {
        const count = this.activePromises.size;
        this.activePromises.clear();
        this.emit('allPromisesCancelled', { count });
        return count;
    }
    
    /**
     * Destructor del gestor
     */
    destroy() {
        this.cancelAllPromises();
        clearInterval(this.cleanupInterval);
        this.eventListeners.clear();
        this.completedPromises.clear();
        this.failedPromises.clear();
    }
}

module.exports = AdvancedPromiseManager;
