<svg width="1100" height="800" xmlns="http://www.w3.org/2000/svg">
  <!-- Definiciones -->
  <defs>
    <!-- Gradientes -->
    <linearGradient id="classGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="asyncGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="awaitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    
    <!-- Sombra -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000" flood-opacity="0.3"/>
    </filter>
    
    <!-- Flecha -->
    <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>

  <!-- Título -->
  <text x="550" y="30" text-anchor="middle" font-size="22" font-weight="bold" fill="#1f2937">
    Anatomía de Métodos Async/Await en Clases
  </text>
  
  <!-- Clase Principal -->
  <rect x="50" y="60" width="1000" height="700" fill="#f8fafc" stroke="#64748b" stroke-width="3" rx="15" filter="url(#shadow)"/>
  <text x="70" y="90" font-size="18" font-weight="bold" fill="#374151">class AdvancedDataManager</text>
  
  <!-- Constructor -->
  <rect x="80" y="110" width="200" height="80" fill="#e0e7ff" stroke="#3b82f6" stroke-width="2" rx="8"/>
  <text x="90" y="130" font-size="14" font-weight="bold" fill="#1e40af">constructor(options)</text>
  <text x="90" y="150" font-size="11" fill="#1e40af">• Configuración inicial</text>
  <text x="90" y="165" font-size="11" fill="#1e40af">• Estado interno</text>
  <text x="90" y="180" font-size="11" fill="#1e40af">• Métricas</text>
  
  <!-- Método Async Principal -->
  <rect x="300" y="110" width="350" height="200" fill="#dcfce7" stroke="#16a34a" stroke-width="3" rx="10" filter="url(#shadow)"/>
  <text x="475" y="135" text-anchor="middle" font-size="16" font-weight="bold" fill="#16a34a">async getData(entityType, id, options)</text>
  
  <!-- Flujo interno del método async -->
  <rect x="320" y="150" width="310" height="150" fill="#f0fdf4" stroke="#16a34a" stroke-width="1" rx="5"/>
  
  <!-- Paso 1: Validación y Cache -->
  <rect x="330" y="160" width="140" height="30" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="400" y="180" text-anchor="middle" font-size="10" fill="#14532d">1. Verificar Cache</text>
  
  <!-- Paso 2: Request -->
  <rect x="480" y="160" width="140" height="30" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="550" y="180" text-anchor="middle" font-size="10" fill="#14532d">2. await executeRequest()</text>
  
  <!-- Paso 3: Procesamiento -->
  <rect x="330" y="200" width="140" height="30" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="400" y="220" text-anchor="middle" font-size="10" fill="#14532d">3. Procesar Respuesta</text>
  
  <!-- Paso 4: Cache Update -->
  <rect x="480" y="200" width="140" height="30" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="550" y="220" text-anchor="middle" font-size="10" fill="#14532d">4. Actualizar Cache</text>
  
  <!-- Paso 5: Return -->
  <rect x="405" y="240" width="140" height="30" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="3"/>
  <text x="475" y="260" text-anchor="middle" font-size="10" fill="#14532d">5. return resultado</text>
  
  <!-- Manejo de Errores -->
  <rect x="680" y="110" width="200" height="200" fill="#fee2e2" stroke="#dc2626" stroke-width="2" rx="8"/>
  <text x="780" y="135" text-anchor="middle" font-size="14" font-weight="bold" fill="#dc2626">Error Handling</text>
  
  <rect x="690" y="150" width="180" height="25" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="780" y="167" text-anchor="middle" font-size="10" fill="#7f1d1d">try/catch wrapper</text>
  
  <rect x="690" y="180" width="180" height="25" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="780" y="197" text-anchor="middle" font-size="10" fill="#7f1d1d">enhanceError()</text>
  
  <rect x="690" y="210" width="180" height="25" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="780" y="227" text-anchor="middle" font-size="10" fill="#7f1d1d">updateMetrics(false)</text>
  
  <rect x="690" y="240" width="180" height="25" fill="#fca5a5" stroke="#dc2626" stroke-width="1" rx="3"/>
  <text x="780" y="257" text-anchor="middle" font-size="10" fill="#7f1d1d">throw enhanced error</text>
  
  <!-- Métodos de Utilidad Async -->
  <rect x="80" y="330" width="940" height="180" fill="#f1f5f9" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="90" y="355" font-size="16" font-weight="bold" fill="#374151">Métodos de Utilidad Async</text>
  
  <!-- executeWithRetry -->
  <rect x="100" y="370" width="200" height="120" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="200" y="390" text-anchor="middle" font-size="12" font-weight="bold" fill="#92400e">executeWithRetry()</text>
  <text x="110" y="410" font-size="10" fill="#92400e">• Backoff exponencial</text>
  <text x="110" y="425" font-size="10" fill="#92400e">• Máximo reintentos</text>
  <text x="110" y="440" font-size="10" fill="#92400e">• Error classification</text>
  <text x="110" y="455" font-size="10" fill="#92400e">• await operation()</text>
  <text x="110" y="470" font-size="10" fill="#92400e">• await delay(backoff)</text>
  
  <!-- validateEntityData -->
  <rect x="320" y="370" width="200" height="120" fill="#ede9fe" stroke="#8b5cf6" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="420" y="390" text-anchor="middle" font-size="12" font-weight="bold" fill="#7c3aed">validateEntityData()</text>
  <text x="330" y="410" font-size="10" fill="#7c3aed">• await getSchema()</text>
  <text x="330" y="425" font-size="10" fill="#7c3aed">• Validación estructura</text>
  <text x="330" y="440" font-size="10" fill="#7c3aed">• await asyncValidations</text>
  <text x="330" y="455" font-size="10" fill="#7c3aed">• Verificar unicidad</text>
  <text x="330" y="470" font-size="10" fill="#7c3aed">• Validar referencias</text>
  
  <!-- processBatch -->
  <rect x="540" y="370" width="200" height="120" fill="#cffafe" stroke="#06b6d4" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="640" y="390" text-anchor="middle" font-size="12" font-weight="bold" fill="#0c4a6e">processBatch()</text>
  <text x="550" y="410" font-size="10" fill="#0c4a6e">• Dividir en lotes</text>
  <text x="550" y="425" font-size="10" fill="#0c4a6e">• await Promise.all()</text>
  <text x="550" y="440" font-size="10" fill="#0c4a6e">• Control concurrencia</text>
  <text x="550" y="455" font-size="10" fill="#0c4a6e">• await onProgress()</text>
  <text x="550" y="470" font-size="10" fill="#0c4a6e">• await delay(batch)</text>
  
  <!-- invalidateRelatedCache -->
  <rect x="760" y="370" width="200" height="120" fill="#fce7f3" stroke="#ec4899" stroke-width="2" rx="5" filter="url(#shadow)"/>
  <text x="860" y="390" text-anchor="middle" font-size="12" font-weight="bold" fill="#be185d">invalidateCache()</text>
  <text x="770" y="410" font-size="10" fill="#be185d">• Cache directo</text>
  <text x="770" y="425" font-size="10" fill="#be185d">• Búsquedas relacionadas</text>
  <text x="770" y="440" font-size="10" fill="#be185d">• await getRelations()</text>
  <text x="770" y="455" font-size="10" fill="#be185d">• Invalidar relaciones</text>
  <text x="770" y="470" font-size="10" fill="#be185d">• return count</text>
  
  <!-- Patrones de Await -->
  <rect x="80" y="530" width="940" height="200" fill="#f8fafc" stroke="#64748b" stroke-width="2" rx="10"/>
  <text x="90" y="555" font-size="16" font-weight="bold" fill="#374151">Patrones de Await en la Clase</text>
  
  <!-- Await Secuencial -->
  <rect x="100" y="570" width="280" height="140" fill="#e0f2fe" stroke="#0891b2" stroke-width="2" rx="8"/>
  <text x="240" y="590" text-anchor="middle" font-size="14" font-weight="bold" fill="#0c4a6e">Await Secuencial</text>
  <text x="110" y="610" font-size="11" font-family="monospace" fill="#0c4a6e">const user = await getData('users', 1);</text>
  <text x="110" y="625" font-size="11" font-family="monospace" fill="#0c4a6e">const posts = await getData('posts', </text>
  <text x="110" y="640" font-size="11" font-family="monospace" fill="#0c4a6e">  {userId: user.id});</text>
  <text x="110" y="655" font-size="11" font-family="monospace" fill="#0c4a6e">const comments = await getData(</text>
  <text x="110" y="670" font-size="11" font-family="monospace" fill="#0c4a6e">  'comments', {postId: posts[0].id});</text>
  <text x="110" y="690" font-size="10" fill="#0c4a6e">✓ Orden garantizado</text>
  <text x="110" y="705" font-size="10" fill="#0c4a6e">⚠ Más lento (secuencial)</text>
  
  <!-- Await Paralelo -->
  <rect x="400" y="570" width="280" height="140" fill="#f0fdf4" stroke="#16a34a" stroke-width="2" rx="8"/>
  <text x="540" y="590" text-anchor="middle" font-size="14" font-weight="bold" fill="#16a34a">Await Paralelo</text>
  <text x="410" y="610" font-size="11" font-family="monospace" fill="#16a34a">const [user, posts, settings] = </text>
  <text x="410" y="625" font-size="11" font-family="monospace" fill="#16a34a">  await Promise.all([</text>
  <text x="410" y="640" font-size="11" font-family="monospace" fill="#16a34a">    getData('users', 1),</text>
  <text x="410" y="655" font-size="11" font-family="monospace" fill="#16a34a">    getData('posts', {userId: 1}),</text>
  <text x="410" y="670" font-size="11" font-family="monospace" fill="#16a34a">    getData('settings', 1)</text>
  <text x="410" y="685" font-size="11" font-family="monospace" fill="#16a34a">  ]);</text>
  <text x="410" y="705" font-size="10" fill="#16a34a">✓ Más rápido (paralelo)</text>
  
  <!-- Await con Control de Errores -->
  <rect x="700" y="570" width="280" height="140" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
  <text x="840" y="590" text-anchor="middle" font-size="14" font-weight="bold" fill="#92400e">Await + Error Control</text>
  <text x="710" y="610" font-size="11" font-family="monospace" fill="#92400e">try {</text>
  <text x="710" y="625" font-size="11" font-family="monospace" fill="#92400e">  const data = await this</text>
  <text x="710" y="640" font-size="11" font-family="monospace" fill="#92400e">    .executeWithRetry(() =></text>
  <text x="710" y="655" font-size="11" font-family="monospace" fill="#92400e">      this.getData(type, id));</text>
  <text x="710" y="670" font-size="11" font-family="monospace" fill="#92400e">} catch (error) {</text>
  <text x="710" y="685" font-size="11" font-family="monospace" fill="#92400e">  throw this.enhanceError(error);</text>
  <text x="710" y="700" font-size="11" font-family="monospace" fill="#92400e">}</text>
  
  <!-- Flechas de flujo -->
  <line x1="280" y1="150" x2="300" y2="150" stroke="#374151" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="650" y1="210" x2="680" y2="210" stroke="#dc2626" stroke-width="2" marker-end="url(#arrow)"/>
  
  <!-- Conexiones entre métodos -->
  <line x1="475" y1="310" x2="475" y2="330" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="200" y1="490" x2="200" y2="530" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="420" y1="490" x2="420" y2="530" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="640" y1="490" x2="640" y2="530" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  <line x1="860" y1="490" x2="860" y2="530" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3"/>
  
  <!-- Etiquetas de conexión -->
  <text x="480" y="325" text-anchor="middle" font-size="9" fill="#64748b">utiliza</text>
  <text x="205" y="525" text-anchor="middle" font-size="9" fill="#64748b">implementa</text>
  <text x="425" y="525" text-anchor="middle" font-size="9" fill="#64748b">implementa</text>
  <text x="645" y="525" text-anchor="middle" font-size="9" fill="#64748b">implementa</text>
  <text x="865" y="525" text-anchor="middle" font-size="9" fill="#64748b">implementa</text>
  
  <!-- Indicadores de estado async -->
  <circle cx="320" cy="125" r="8" fill="#10b981"/>
  <text x="335" y="130" font-size="10" fill="#10b981">async</text>
  
  <circle cx="110" cy="385" r="6" fill="#f59e0b"/>
  <text x="125" y="390" font-size="9" fill="#f59e0b">async</text>
  
  <circle cx="330" cy="385" r="6" fill="#8b5cf6"/>
  <text x="345" y="390" font-size="9" fill="#8b5cf6">async</text>
  
  <circle cx="550" cy="385" r="6" fill="#06b6d4"/>
  <text x="565" y="390" font-size="9" fill="#06b6d4">async</text>
  
  <circle cx="770" cy="385" r="6" fill="#ec4899"/>
  <text x="785" y="390" font-size="9" fill="#ec4899">async</text>
  
  <!-- Leyenda -->
  <rect x="900" y="60" width="140" height="100" fill="#f8fafc" stroke="#64748b" stroke-width="1" rx="5" opacity="0.95"/>
  <text x="910" y="80" font-size="12" font-weight="bold" fill="#374151">LEYENDA:</text>
  <circle cx="920" cy="95" r="6" fill="#10b981"/>
  <text x="935" y="100" font-size="10" fill="#374151">Método async</text>
  <rect x="910" y="105" width="15" height="8" fill="#bbf7d0" stroke="#16a34a" stroke-width="1" rx="2"/>
  <text x="935" y="113" font-size="10" fill="#374151">Paso await</text>
  <line x1="910" y1="125" x2="925" y2="125" stroke="#dc2626" stroke-width="2"/>
  <text x="935" y="130" font-size="10" fill="#374151">Error flow</text>
  <line x1="910" y1="140" x2="925" y2="140" stroke="#64748b" stroke-width="1" stroke-dasharray="2,2"/>
  <text x="935" y="145" font-size="10" fill="#374151">Dependencia</text>
</svg>
