# **52.1 - FUNDAMENTOS DE PROMESAS EN CLASES**

## **📖 INTRODUCCIÓN**

Las promesas representan la evolución natural del manejo asíncrono en JavaScript, transformando el caótico "callback hell" en un flujo elegante y predecible que se integra perfectamente con la programación orientada a objetos. En el contexto de las clases ES6, las promesas no son simplemente una herramienta de conveniencia, sino el fundamento arquitectónico que permite crear sistemas robustos, escalables y mantenibles que manejan operaciones asíncronas de manera profesional. Dominar las promesas en el contexto de OOP te permitirá diseñar APIs fluidas, implementar patrones de retry sofisticados, crear sistemas de cache inteligentes y construir arquitecturas reactivas que respondan elegantemente a eventos asincrónicos. En aplicaciones empresariales modernas, las promesas son esenciales para integrar servicios externos, manejar bases de datos, procesar archivos y coordinar múltiples operaciones concurrentes, convirtiendo el código asíncrono en una sinfonía orquestada de operaciones que se ejecutan con precisión y gracia. La maestría en promesas dentro de clases no solo mejorará la calidad de tu código, sino que te posicionará como un arquitecto de software capaz de diseñar sistemas que escalan y evolucionan con las demandas del negocio.

## **🔍 CONCEPTOS FUNDAMENTALES**

### **Estados de una Promesa**
```javascript
// Estados básicos de una promesa
const promiseStates = {
    PENDING: 'pending',     // Estado inicial
    FULFILLED: 'fulfilled', // Operación completada exitosamente
    REJECTED: 'rejected'    // Operación falló
};
```

### **Anatomía de una Promesa en Clase**

![Anatomía de Promesas](../VISUALIZACIONES/anatomia/promise-anatomy.svg)

```javascript
class SimplePromiseExample {
    constructor() {
        // Estado interno de la clase para almacenar datos
        this.data = null;
        this.isLoading = false;
        this.lastError = null;
    }

    /**
     * Método que retorna una promesa - Patrón Producer
     * @param {string} url - URL para simular fetch
     * @returns {Promise<Object>} Promesa que resuelve con datos
     */
    async fetchData(url) {
        return new Promise((resolve, reject) => {
            // Simulación de operación asíncrona (API call, DB query, etc.)
            setTimeout(() => {
                if (url && url.length > 0) {
                    // Resolución exitosa con datos estructurados
                    resolve({
                        data: 'Success!',
                        url,
                        timestamp: Date.now(),
                        status: 'completed'
                    });
                } else {
                    // Rechazo con error descriptivo
                    reject(new Error('URL is required and cannot be empty'));
                }
            }, 1000); // Simula latencia de red
        });
    }

    /**
     * Método que consume promesas - Patrón Consumer
     * @param {string} url - URL para cargar datos
     * @returns {Promise<Object>} Datos cargados
     */
    async loadData(url) {
        // Indicador de estado de carga
        this.isLoading = true;
        this.lastError = null;

        try {
            // Await pausa la ejecución hasta que la promesa se resuelve
            this.data = await this.fetchData(url);
            console.log(`✅ Datos cargados exitosamente para: ${url}`);
            return this.data;
        } catch (error) {
            // Captura y manejo de errores
            this.lastError = error;
            console.error(`❌ Error cargando datos: ${error.message}`);
            throw error; // Re-lanza para que el caller pueda manejar
        } finally {
            // Siempre se ejecuta, sin importar éxito o fallo
            this.isLoading = false;
        }
    }
}
```

### **🔍 EXPLICACIÓN EXHAUSTIVA DEL CÓDIGO**

**Líneas 1-7: Constructor y Estado**
- El constructor inicializa el estado interno de la clase
- `data`: Almacena los datos obtenidos de operaciones asíncronas
- `isLoading`: Flag booleano para indicar operaciones en progreso
- `lastError`: Referencia al último error ocurrido para debugging

**Líneas 13-29: Método fetchData (Producer Pattern)**
- **Línea 14**: `new Promise()` crea una nueva promesa con executor function
- **Línea 16**: `setTimeout()` simula operación asíncrona (red, DB, archivo)
- **Líneas 17-23**: Lógica de resolución exitosa con objeto estructurado
- **Líneas 24-26**: Lógica de rechazo con error descriptivo
- **Línea 27**: El delay simula latencia real de operaciones I/O

**Líneas 35-52: Método loadData (Consumer Pattern)**
- **Líneas 37-38**: Actualización de estado antes de operación asíncrona
- **Línea 41**: `await` convierte la promesa en valor síncrono
- **Líneas 42-43**: Logging y retorno en caso de éxito
- **Líneas 44-48**: Bloque catch para manejo de errores
- **Líneas 49-51**: Bloque finally para cleanup garantizado

### **💻 EMULACIÓN DE CONSOLA**

```bash
# Creando instancia y ejecutando
const example = new SimplePromiseExample();

# Caso exitoso
await example.loadData('https://api.example.com/users');
✅ Datos cargados exitosamente para: https://api.example.com/users

# Estado después del éxito
console.log(example.data);
{
  data: 'Success!',
  url: 'https://api.example.com/users',
  timestamp: 1703123456789,
  status: 'completed'
}

# Caso de error
await example.loadData('');
❌ Error cargando datos: URL is required and cannot be empty
Error: URL is required and cannot be empty
    at Timeout._onTimeout (SimplePromiseExample.js:26)

# Estado después del error
console.log(example.lastError.message);
"URL is required and cannot be empty"
```

## **⚙️ CARACTERÍSTICAS AVANZADAS**

### **1. Control de Concurrencia**
Las promesas permiten controlar cuántas operaciones asíncronas se ejecutan simultáneamente:

```javascript
class ConcurrencyController {
    constructor(maxConcurrency = 3) {
        this.maxConcurrency = maxConcurrency;
        this.running = 0;
        this.queue = [];
    }
    
    async execute(promiseFactory) {
        return new Promise((resolve, reject) => {
            this.queue.push({ promiseFactory, resolve, reject });
            this.processQueue();
        });
    }
    
    async processQueue() {
        if (this.running >= this.maxConcurrency || this.queue.length === 0) {
            return;
        }
        
        this.running++;
        const { promiseFactory, resolve, reject } = this.queue.shift();
        
        try {
            const result = await promiseFactory();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running--;
            this.processQueue();
        }
    }
}
```

### **2. Timeout y Retry Automático**
```javascript
class PromiseWithTimeout {
    static async withTimeout(promise, timeoutMs) {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout')), timeoutMs);
        });
        
        return Promise.race([promise, timeoutPromise]);
    }
    
    static async withRetry(promiseFactory, maxRetries = 3) {
        let lastError;
        
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await promiseFactory();
            } catch (error) {
                lastError = error;
                if (attempt < maxRetries) {
                    await this.delay(1000 * Math.pow(2, attempt));
                }
            }
        }
        
        throw lastError;
    }
    
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
```

### **3. Promisificación de Callbacks**
```javascript
class PromiseUtils {
    static promisify(fn, context = null) {
        return (...args) => {
            return new Promise((resolve, reject) => {
                const callback = (error, result) => {
                    if (error) reject(error);
                    else resolve(result);
                };
                
                fn.call(context, ...args, callback);
            });
        };
    }
    
    // Ejemplo de uso
    static example() {
        const fs = require('fs');
        const readFileAsync = this.promisify(fs.readFile);
        
        return readFileAsync('file.txt', 'utf8');
    }
}
```

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de Cache Inteligente**
Un sistema de cache que utiliza promesas para manejar la carga de datos de manera eficiente, evitando requests duplicados y proporcionando fallbacks automáticos.

### **Caso 2: Integración de APIs Externas**
Coordinación de múltiples llamadas a APIs externas con manejo de rate limiting, timeouts y retry automático para garantizar robustez en sistemas distribuidos.

### **Caso 3: Procesamiento de Archivos en Lotes**
Sistema que procesa grandes volúmenes de archivos de manera asíncrona, controlando la concurrencia para evitar saturar recursos del sistema.

## **⚠️ ERRORES COMUNES**

### **Error 1: No Manejar Promesas Rechazadas**
```javascript
// ❌ INCORRECTO
class BadExample {
    async loadData() {
        this.fetchData(); // Promise no manejada
    }
}

// ✅ CORRECTO
class GoodExample {
    async loadData() {
        try {
            return await this.fetchData();
        } catch (error) {
            this.handleError(error);
            throw error;
        }
    }
}
```

### **Error 2: Memory Leaks con Promesas**
```javascript
// ❌ INCORRECTO - Acumula promesas
class BadPromiseManager {
    constructor() {
        this.allPromises = []; // Nunca se limpia
    }
}

// ✅ CORRECTO - Limpieza automática
class GoodPromiseManager {
    constructor() {
        this.activePromises = new Map();
        this.startCleanup();
    }
    
    startCleanup() {
        setInterval(() => this.cleanup(), 60000);
    }
}
```

### **Error 3: Bloqueo del Event Loop**
```javascript
// ❌ INCORRECTO - Operación síncrona pesada
async function badProcessing(items) {
    return items.map(item => heavyProcessing(item));
}

// ✅ CORRECTO - Procesamiento asíncrono
async function goodProcessing(items) {
    const results = [];
    for (const item of items) {
        results.push(await processAsync(item));
        await new Promise(resolve => setImmediate(resolve)); // Yield
    }
    return results;
}
```

## **💡 MEJORES PRÁCTICAS**

### **1. Siempre Manejar Errores**
Toda promesa debe tener manejo de errores explícito, ya sea con `.catch()` o `try/catch` con async/await.

### **2. Usar Promise.all() para Operaciones Paralelas**
Cuando las operaciones son independientes, ejecutarlas en paralelo mejora significativamente el performance.

### **3. Implementar Timeouts Apropiados**
Toda operación asíncrona debe tener un timeout para evitar que la aplicación se cuelgue indefinidamente.

### **4. Controlar la Concurrencia**
En operaciones que consumen muchos recursos, implementar control de concurrencia para mantener estabilidad del sistema.

### **5. Documentar Comportamiento Asíncrono**
Claramente documentar qué métodos son asíncronos y qué promesas retornan para facilitar el uso por otros desarrolladores.

## **🤔 PREGUNTAS PARA REFLEXIÓN**

- ¿Cómo diseñarías un sistema de prioridades para promesas que permita ejecutar operaciones críticas antes que las de baja prioridad?
- ¿Qué estrategias implementarías para manejar promesas que dependen unas de otras en un grafo de dependencias complejo?
- ¿Cómo optimizarías el sistema para diferentes patrones de carga (picos vs carga constante)?
- ¿De qué manera integrarías este gestor con sistemas de observabilidad para tracing distribuido?

## **📚 RECURSOS ADICIONALES**

- **Implementación Completa:** Ver `CODIGO/promesas/PromiseManager.js`
- **Ejemplos Prácticos:** Ver `EJEMPLOS/basicos/ejemplo-promesas.js`
- **Tests:** Ver `TESTS/unit/promise-manager.test.js`
- **Visualizaciones:** Ver `VISUALIZACIONES/anatomia/promise-anatomy.svg`

---

**Próximo:** [52.2 - Async/Await en Métodos de Clase](52.2%20-%20Async%20Await%20Conceptos.md)
