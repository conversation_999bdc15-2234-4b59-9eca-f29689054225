# **43.1 - FUNDAMENTOS DE LA PROGRAMACIÓN ORIENTADA A OBJETOS**

## **📖 INTRODUCCIÓN**

La Programación Orientada a Objetos representa un paradigma fundamental que revolucionó el desarrollo de software al proporcionar una metodología natural para modelar problemas del mundo real mediante la creación de objetos que encapsulan datos y comportamientos relacionados, estableciendo así una correspondencia directa entre los conceptos del dominio del problema y las estructuras del código. En JavaScript, este paradigma adquiere características únicas debido a la naturaleza dinámica y flexible del lenguaje, que permite implementar conceptos OOP de maneras innovadoras que van más allá de las implementaciones tradicionales encontradas en lenguajes como Java o C#. Comprender estos fundamentos no solo es esencial para escribir código JavaScript efectivo, sino que también proporciona la base conceptual necesaria para aprovechar frameworks modernos, bibliotecas de terceros, y patrones arquitectónicos que dominan el ecosistema de desarrollo web actual, donde la capacidad de crear abstracciones robustas y mantenibles determina la diferencia entre aplicaciones exitosas y proyectos que se vuelven inmanejables con el tiempo.

## **🔍 CONCEPTOS FUNDAMENTALES**

### **43.1.1. Definición y concepto de OOP**

![Concepto OOP](../VISUALIZACIONES/anatomia/oop-concept.svg)

La Programación Orientada a Objetos (OOP) es un paradigma de programación que organiza el código alrededor de **objetos** en lugar de funciones y lógica. Un objeto es una entidad que combina:

- **Datos** (propiedades/atributos)
- **Comportamientos** (métodos/funciones)
- **Identidad** (referencia única)

```javascript
/**
 * EJEMPLO FUNDAMENTAL: ¿QUÉ ES UN OBJETO?
 * 
 * Un objeto representa una entidad del mundo real con:
 * - Estado (propiedades)
 * - Comportamiento (métodos)
 * - Identidad (referencia única)
 */

// Objeto literal básico
const persona = {
    // ESTADO - Propiedades que describen el objeto
    nombre: 'Juan',
    edad: 30,
    profesion: 'Desarrollador',
    
    // COMPORTAMIENTO - Métodos que definen qué puede hacer
    saludar() {
        return `Hola, soy ${this.nombre}`;
    },
    
    cumplirAños() {
        this.edad++;
        console.log(`${this.nombre} ahora tiene ${this.edad} años`);
    },
    
    trabajar() {
        console.log(`${this.nombre} está programando...`);
    }
};

// IDENTIDAD - Cada objeto tiene una referencia única
const persona1 = { nombre: 'Ana' };
const persona2 = { nombre: 'Ana' };
console.log(persona1 === persona2); // false - diferentes identidades
```

### **43.1.2. Historia y evolución de OOP**

```javascript
/**
 * LÍNEA TEMPORAL DE OOP
 * 
 * Evolución histórica de la programación orientada a objetos
 */

const historiaOOP = {
    1960: {
        lenguaje: "Simula 67",
        aporte: "Primer lenguaje con conceptos de clases y objetos",
        innovacion: "Introdujo clases, objetos y herencia",
        impacto: "Fundación conceptual de OOP"
    },
    
    1972: {
        lenguaje: "Smalltalk",
        aporte: "Primer lenguaje puramente orientado a objetos",
        innovacion: "Todo es un objeto, paso de mensajes",
        impacto: "Estableció principios puros de OOP"
    },
    
    1983: {
        lenguaje: "C++",
        aporte: "OOP en lenguaje de sistemas",
        innovacion: "Herencia múltiple, sobrecarga",
        impacto: "Popularizó OOP en desarrollo de sistemas"
    },
    
    1995: {
        lenguaje: "Java",
        aporte: "OOP empresarial con garbage collection",
        innovacion: "Write once, run anywhere",
        impacto: "Masificó OOP en desarrollo empresarial"
    },
    
    1995: {
        lenguaje: "JavaScript",
        aporte: "OOP basada en prototipos",
        innovacion: "Herencia prototipal, objetos dinámicos",
        impacto: "OOP flexible para desarrollo web"
    },
    
    2015: {
        lenguaje: "ES6 JavaScript",
        aporte: "Sintaxis de clases moderna",
        innovacion: "Clases, herencia, métodos estáticos",
        impacto: "Facilitó adopción de OOP en JavaScript"
    }
};

// Función para mostrar la evolución
function mostrarEvolucionOOP() {
    console.log('📚 EVOLUCIÓN DE LA PROGRAMACIÓN ORIENTADA A OBJETOS\n');
    
    Object.entries(historiaOOP).forEach(([año, info]) => {
        console.log(`${año}: ${info.lenguaje}`);
        console.log(`   📝 ${info.aporte}`);
        console.log(`   💡 Innovación: ${info.innovacion}`);
        console.log(`   🎯 Impacto: ${info.impacto}\n`);
    });
}
```

### **43.1.3. Paradigmas de programación**

```javascript
/**
 * COMPARACIÓN DE PARADIGMAS
 * 
 * El mismo problema resuelto con diferentes paradigmas
 * Problema: Gestionar una biblioteca de libros
 */

// ===== PARADIGMA IMPERATIVO =====
// Enfoque: Describe CÓMO hacer las cosas paso a paso
function gestionBibliotecaImperativo() {
    let libros = [];
    let prestamos = [];
    
    // Agregar libro
    function agregarLibro(titulo, autor) {
        let libro = {
            id: libros.length + 1,
            titulo: titulo,
            autor: autor,
            disponible: true
        };
        libros.push(libro);
        return libro.id;
    }
    
    // Prestar libro
    function prestarLibro(id, usuario) {
        for (let i = 0; i < libros.length; i++) {
            if (libros[i].id === id && libros[i].disponible) {
                libros[i].disponible = false;
                prestamos.push({
                    libroId: id,
                    usuario: usuario,
                    fecha: new Date()
                });
                return true;
            }
        }
        return false;
    }
    
    return { agregarLibro, prestarLibro, libros, prestamos };
}

// ===== PARADIGMA FUNCIONAL =====
// Enfoque: Funciones puras, inmutabilidad
const gestionBibliotecaFuncional = (() => {
    const crearLibro = (id, titulo, autor) => ({
        id,
        titulo,
        autor,
        disponible: true
    });
    
    const agregarLibro = (biblioteca, titulo, autor) => {
        const nuevoId = biblioteca.libros.length + 1;
        const nuevoLibro = crearLibro(nuevoId, titulo, autor);
        
        return {
            ...biblioteca,
            libros: [...biblioteca.libros, nuevoLibro]
        };
    };
    
    const prestarLibro = (biblioteca, id, usuario) => {
        const librosActualizados = biblioteca.libros.map(libro =>
            libro.id === id && libro.disponible
                ? { ...libro, disponible: false }
                : libro
        );
        
        const nuevoPrestamo = {
            libroId: id,
            usuario,
            fecha: new Date()
        };
        
        return {
            libros: librosActualizados,
            prestamos: [...biblioteca.prestamos, nuevoPrestamo]
        };
    };
    
    return { agregarLibro, prestarLibro };
})();

// ===== PARADIGMA ORIENTADO A OBJETOS =====
// Enfoque: Modela conceptos como objetos con estado y comportamiento
class Libro {
    constructor(id, titulo, autor) {
        this.id = id;
        this.titulo = titulo;
        this.autor = autor;
        this.disponible = true;
        this.historialPrestamos = [];
    }
    
    prestar(usuario) {
        if (!this.disponible) {
            throw new Error('Libro no disponible');
        }
        
        this.disponible = false;
        this.historialPrestamos.push({
            usuario,
            fechaPrestamo: new Date(),
            fechaDevolucion: null
        });
        
        console.log(`"${this.titulo}" prestado a ${usuario}`);
    }
    
    devolver() {
        if (this.disponible) {
            throw new Error('Libro ya está disponible');
        }
        
        this.disponible = true;
        const ultimoPrestamo = this.historialPrestamos[this.historialPrestamos.length - 1];
        ultimoPrestamo.fechaDevolucion = new Date();
        
        console.log(`"${this.titulo}" devuelto`);
    }
    
    obtenerInfo() {
        return {
            id: this.id,
            titulo: this.titulo,
            autor: this.autor,
            disponible: this.disponible,
            totalPrestamos: this.historialPrestamos.length
        };
    }
}

class Biblioteca {
    constructor(nombre) {
        this.nombre = nombre;
        this.libros = new Map();
        this.siguienteId = 1;
    }
    
    agregarLibro(titulo, autor) {
        const libro = new Libro(this.siguienteId++, titulo, autor);
        this.libros.set(libro.id, libro);
        
        console.log(`Libro "${titulo}" agregado a ${this.nombre}`);
        return libro.id;
    }
    
    prestarLibro(id, usuario) {
        const libro = this.libros.get(id);
        
        if (!libro) {
            throw new Error('Libro no encontrado');
        }
        
        libro.prestar(usuario);
    }
    
    devolverLibro(id) {
        const libro = this.libros.get(id);
        
        if (!libro) {
            throw new Error('Libro no encontrado');
        }
        
        libro.devolver();
    }
    
    obtenerEstadisticas() {
        const totalLibros = this.libros.size;
        const librosDisponibles = Array.from(this.libros.values())
            .filter(libro => libro.disponible).length;
        const librosPrestados = totalLibros - librosDisponibles;
        
        return {
            nombre: this.nombre,
            totalLibros,
            librosDisponibles,
            librosPrestados,
            tasaOcupacion: ((librosPrestados / totalLibros) * 100).toFixed(1) + '%'
        };
    }
}
```

### **💻 EMULACIÓN DE CONSOLA**

```bash
# Comparación de paradigmas en acción

# === PARADIGMA IMPERATIVO ===
const bibliotecaImperativo = gestionBibliotecaImperativo();
bibliotecaImperativo.agregarLibro('1984', 'George Orwell');
bibliotecaImperativo.prestarLibro(1, 'Juan');
console.log(bibliotecaImperativo.libros);
[
  {
    id: 1,
    titulo: '1984',
    autor: 'George Orwell',
    disponible: false
  }
]

# === PARADIGMA FUNCIONAL ===
let bibliotecaFuncional = { libros: [], prestamos: [] };
bibliotecaFuncional = gestionBibliotecaFuncional.agregarLibro(
    bibliotecaFuncional, 
    'El Quijote', 
    'Cervantes'
);
bibliotecaFuncional = gestionBibliotecaFuncional.prestarLibro(
    bibliotecaFuncional, 
    1, 
    'María'
);
console.log(bibliotecaFuncional);
{
  libros: [
    {
      id: 1,
      titulo: 'El Quijote',
      autor: 'Cervantes',
      disponible: false
    }
  ],
  prestamos: [
    {
      libroId: 1,
      usuario: 'María',
      fecha: 2024-01-15T10:30:45.123Z
    }
  ]
}

# === PARADIGMA ORIENTADO A OBJETOS ===
const biblioteca = new Biblioteca('Biblioteca Central');
const libroId = biblioteca.agregarLibro('Cien años de soledad', 'García Márquez');
biblioteca.prestarLibro(libroId, 'Carlos');

console.log(biblioteca.obtenerEstadisticas());
{
  nombre: 'Biblioteca Central',
  totalLibros: 1,
  librosDisponibles: 0,
  librosPrestados: 1,
  tasaOcupacion: '100.0%'
}

Libro "Cien años de soledad" agregado a Biblioteca Central
"Cien años de soledad" prestado a Carlos
```

### **43.1.4. Ventajas y desventajas de OOP**

**✅ Ventajas:**
- **Modelado natural:** Representa conceptos del mundo real
- **Reutilización:** Herencia y composición permiten reutilizar código
- **Encapsulación:** Oculta detalles internos y expone interfaces limpias
- **Mantenibilidad:** Código organizado y modular
- **Escalabilidad:** Fácil agregar nuevas funcionalidades

**❌ Desventajas:**
- **Complejidad:** Puede ser excesivo para problemas simples
- **Performance:** Overhead de objetos y métodos
- **Curva de aprendizaje:** Requiere entender conceptos abstractos
- **Over-engineering:** Tendencia a crear jerarquías innecesarias

### **43.1.5. Objetos y clases**

En JavaScript, los objetos pueden crearse de múltiples formas:

```javascript
// 1. Objeto literal
const persona1 = {
    nombre: 'Ana',
    saludar() { return `Hola, soy ${this.nombre}`; }
};

// 2. Función constructora
function Persona(nombre) {
    this.nombre = nombre;
    this.saludar = function() {
        return `Hola, soy ${this.nombre}`;
    };
}
const persona2 = new Persona('Luis');

// 3. Clase ES6
class PersonaClase {
    constructor(nombre) {
        this.nombre = nombre;
    }
    
    saludar() {
        return `Hola, soy ${this.nombre}`;
    }
}
const persona3 = new PersonaClase('María');
```

## **🎯 CASOS DE USO PRÁCTICOS**

### **Caso 1: Sistema de E-commerce**
Modelar productos, usuarios, carritos de compra y pedidos como objetos interrelacionados.

### **Caso 2: Juego de Rol**
Crear personajes, items, habilidades y sistemas de combate usando OOP.

### **Caso 3: Aplicación de Gestión**
Organizar empleados, departamentos, proyectos y tareas en una estructura orientada a objetos.

## **💡 MEJORES PRÁCTICAS**

### **1. Principio de Responsabilidad Única**
Cada clase debe tener una sola razón para cambiar.

### **2. Encapsulación Apropiada**
Oculta detalles internos y expone solo lo necesario.

### **3. Composición sobre Herencia**
Prefiere componer objetos antes que crear jerarquías complejas.

### **4. Interfaces Claras**
Diseña métodos públicos intuitivos y consistentes.

## **🤔 PREGUNTAS PARA REFLEXIÓN**

- ¿Cuándo es apropiado usar OOP versus otros paradigmas?
- ¿Cómo decides qué debe ser una clase y qué debe ser una función?
- ¿Qué ventajas ofrece JavaScript para implementar OOP?
- ¿Cómo balanceas flexibilidad y estructura en el diseño de objetos?

## **📚 RECURSOS ADICIONALES**

- **Implementación Completa:** Ver `CODIGO/fundamentos/OOPFoundations.js`
- **Ejemplos Prácticos:** Ver `EJEMPLOS/basicos/oop-fundamentals.js`
- **Tests:** Ver `TESTS/unit/oop-foundations.test.js`
- **Visualizaciones:** Ver `VISUALIZACIONES/anatomia/oop-concept.svg`

---

**Continúa con:** `43.2 - Pilares de la Programación Orientada a Objetos.md`
