## **PARTE VI: DOM Y EVENTOS**

### **Capítulo 69: Introducción al DOM**

#### **69.1. Fundamentos del DOM**
69.1.1. ¿Qué es el DOM?
69.1.2. DOM Tree Structure
69.1.3. Nodes y Elements
69.1.4. DOM Interfaces
69.1.5. DOM Levels
69.1.6. Browser Implementation
69.1.7. Performance Considerations
69.1.8. DOM vs Virtual DOM
69.1.9. Shadow DOM
69.1.10. Best Practices

#### **69.2. Navegación del DOM**
69.2.1. Parent-Child Relationships
69.2.2. Sibling Navigation
69.2.3. Tree Traversal
69.2.4. NodeList vs HTMLCollection
69.2.5. Live vs Static Collections
69.2.6. Performance Optimization
69.2.7. Common Patterns
69.2.8. <PERSON><PERSON>r Handling
69.2.9. Cross-browser Compatibility
69.2.10. Best Practices

#### **69.3. DOM Properties**
69.3.1. Element Properties
69.3.2. Node Properties
69.3.3. Document Properties
69.3.4. Window Properties
69.3.5. Computed Styles
69.3.6. Bounding Rectangles
69.3.7. Scroll Properties
69.3.8. Visibility Properties
69.3.9. Performance Impact
69.3.10. Best Practices

### **Capítulo 70: Selección y Manipulación de Elementos**

#### **70.1. Métodos de Selección**
70.1.1. getElementById()
70.1.2. getElementsByClassName()
70.1.3. getElementsByTagName()
70.1.4. querySelector()
70.1.5. querySelectorAll()
70.1.6. Performance Comparison
70.1.7. CSS Selector Syntax
70.1.8. Pseudo-selectors
70.1.9. Attribute Selectors
70.1.10. Best Practices

#### **70.2. Manipulación de Contenido**
70.2.1. innerHTML vs textContent
70.2.2. innerText vs textContent
70.2.3. outerHTML
70.2.4. insertAdjacentHTML()
70.2.5. Security Considerations
70.2.6. Performance Optimization
70.2.7. Memory Management
70.2.8. Cross-browser Issues
70.2.9. Sanitization
70.2.10. Best Practices

#### **70.3. Manipulación de Atributos**
70.3.1. getAttribute() y setAttribute()
70.3.2. removeAttribute()
70.3.3. hasAttribute()
70.3.4. Data Attributes
70.3.5. Boolean Attributes
70.3.6. Custom Attributes
70.3.7. Attribute vs Property
70.3.8. Performance Considerations
70.3.9. Validation
70.3.10. Best Practices

### **Capítulo 71: Creación y Modificación de Elementos**

#### **71.1. Creación de Elementos**
71.1.1. createElement()
71.1.2. createTextNode()
71.1.3. createDocumentFragment()
71.1.4. cloneNode()
71.1.5. importNode()
71.1.6. Template Elements
71.1.7. Custom Elements
71.1.8. Performance Optimization
71.1.9. Memory Management
71.1.10. Best Practices

#### **71.2. Inserción de Elementos**
71.2.1. appendChild()
71.2.2. insertBefore()
71.2.3. insertAdjacentElement()
71.2.4. prepend() y append()
71.2.5. before() y after()
71.2.6. replaceWith()
71.2.7. Document Fragments
71.2.8. Batch Operations
71.2.9. Performance Optimization
71.2.10. Best Practices

#### **71.3. Eliminación de Elementos**
71.3.1. removeChild()
71.3.2. remove()
71.3.3. replaceChild()
71.3.4. Memory Cleanup
71.3.5. Event Listener Cleanup
71.3.6. Reference Cleanup
71.3.7. Performance Considerations
71.3.8. Garbage Collection
71.3.9. Common Pitfalls
71.3.10. Best Practices

### **Capítulo 72: Estilos y CSS desde JavaScript**

#### **72.1. Manipulación de Estilos**
72.1.1. style Property
72.1.2. cssText Property
72.1.3. getComputedStyle()
72.1.4. CSS Custom Properties
72.1.5. CSS-in-JS
72.1.6. Style Sheets API
72.1.7. CSS Object Model
72.1.8. Performance Optimization
72.1.9. Browser Compatibility
72.1.10. Best Practices

#### **72.2. Clases CSS**
72.2.1. className Property
72.2.2. classList API
72.2.3. add() y remove()
72.2.4. toggle()
72.2.5. contains()
72.2.6. replace()
72.2.7. Multiple Classes
72.2.8. Conditional Classes
72.2.9. Performance Considerations
72.2.10. Best Practices

#### **72.3. Animaciones CSS**
72.3.1. CSS Transitions
72.3.2. CSS Animations
72.3.3. Transform Properties
72.3.4. Animation Events
72.3.5. Performance Optimization
72.3.6. Hardware Acceleration
72.3.7. Composite Layers
72.3.8. Animation Libraries
72.3.9. Debugging Animations
72.3.10. Best Practices

### **Capítulo 73: Eventos - Fundamentos**

#### **73.1. Sistema de Eventos**
73.1.1. Event-driven Programming
73.1.2. Event Types
73.1.3. Event Objects
73.1.4. Event Flow
73.1.5. Event Phases
73.1.6. Event Propagation
73.1.7. Event Bubbling
73.1.8. Event Capturing
73.1.9. Event Delegation
73.1.10. Performance Considerations

#### **73.2. Event Listeners**
73.2.1. addEventListener()
73.2.2. removeEventListener()
73.2.3. Event Handler Properties
73.2.4. Inline Event Handlers
73.2.5. Event Options
73.2.6. Passive Listeners
73.2.7. Once Option
73.2.8. Signal Option
73.2.9. Memory Management
73.2.10. Best Practices

#### **73.3. Event Object**
73.3.1. Event Properties
73.3.2. target vs currentTarget
73.3.3. preventDefault()
73.3.4. stopPropagation()
73.3.5. stopImmediatePropagation()
73.3.6. Event Coordinates
73.3.7. Keyboard Events
73.3.8. Mouse Events
73.3.9. Touch Events
73.3.10. Custom Properties

### **Capítulo 74: Eventos - Tipos y Manejo**

#### **74.1. Mouse Events**
74.1.1. click y dblclick
74.1.2. mousedown y mouseup
74.1.3. mouseover y mouseout
74.1.4. mouseenter y mouseleave
74.1.5. mousemove
74.1.6. contextmenu
74.1.7. wheel
74.1.8. Event Coordinates
74.1.9. Button Detection
74.1.10. Performance Optimization

#### **74.2. Keyboard Events**
74.2.1. keydown y keyup
74.2.2. keypress (deprecated)
74.2.3. Key Codes
74.2.4. Key Values
74.2.5. Modifier Keys
74.2.6. Input Method Editor
74.2.7. Accessibility Considerations
74.2.8. Cross-browser Issues
74.2.9. Security Considerations
74.2.10. Best Practices

#### **74.3. Form Events**
74.3.1. submit
74.3.2. input
74.3.3. change
74.3.4. focus y blur
74.3.5. focusin y focusout
74.3.6. invalid
74.3.7. reset
74.3.8. select
74.3.9. Validation Events
74.3.10. Performance Optimization

#### **74.4. Touch Events**
74.4.1. touchstart
74.4.2. touchmove
74.4.3. touchend
74.4.4. touchcancel
74.4.5. Touch Objects
74.4.6. Multi-touch Handling
74.4.7. Gesture Recognition
74.4.8. Performance Optimization
74.4.9. Accessibility
74.4.10. Best Practices

### **Capítulo 75: Event Delegation y Bubbling**

#### **75.1. Event Bubbling**
75.1.1. Bubbling Mechanism
75.1.2. Event Path
75.1.3. Stopping Bubbling
75.1.4. Bubbling Performance
75.1.5. Common Use Cases
75.1.6. Debugging Bubbling
75.1.7. Cross-browser Issues
75.1.8. Best Practices
75.1.9. Anti-patterns
75.1.10. Optimization Techniques

#### **75.2. Event Capturing**
75.2.1. Capturing Phase
75.2.2. useCapture Parameter
75.2.3. Capturing vs Bubbling
75.2.4. Use Cases
75.2.5. Performance Implications
75.2.6. Debugging Capturing
75.2.7. Browser Support
75.2.8. Best Practices
75.2.9. Common Pitfalls
75.2.10. Optimization

#### **75.3. Event Delegation**
75.3.1. Delegation Pattern
75.3.2. Benefits y Drawbacks
75.3.3. Implementation Strategies
75.3.4. Dynamic Content
75.3.5. Performance Benefits
75.3.6. Memory Optimization
75.3.7. Event Filtering
75.3.8. Debugging Delegation
75.3.9. Testing Strategies
75.3.10. Best Practices

### **Capítulo 76: Formularios y Validación**

#### **76.1. Formularios HTML5**
76.1.1. Form Elements
76.1.2. Input Types
76.1.3. Form Attributes
76.1.4. Form Validation
76.1.5. Custom Validity
76.1.6. Constraint Validation API
76.1.7. Form Data API
76.1.8. File Upload
76.1.9. Progressive Enhancement
76.1.10. Accessibility

#### **76.2. Validación del Cliente**
76.2.1. Built-in Validation
76.2.2. Custom Validation
76.2.3. Real-time Validation
76.2.4. Validation Messages
76.2.5. Error Display
76.2.6. Validation Libraries
76.2.7. Performance Optimization
76.2.8. User Experience
76.2.9. Security Considerations
76.2.10. Best Practices

#### **76.3. Envío de Formularios**
76.3.1. Form Submission
76.3.2. AJAX Form Submission
76.3.3. FormData API
76.3.4. File Upload
76.3.5. Progress Tracking
76.3.6. Error Handling
76.3.7. Success Feedback
76.3.8. Preventing Double Submission
76.3.9. Security Measures
76.3.10. Best Practices

### **Capítulo 77: Animaciones con JavaScript**

#### **77.1. Fundamentos de Animación**
77.1.1. Animation Principles
77.1.2. Frame Rate
77.1.3. Timing Functions
77.1.4. Easing
77.1.5. Duration y Delay
77.1.6. Animation Loop
77.1.7. Performance Considerations
77.1.8. Hardware Acceleration
77.1.9. Browser Optimization
77.1.10. Best Practices

#### **77.2. requestAnimationFrame**
77.2.1. RAF API
77.2.2. Animation Loop
77.2.3. Timing Control
77.2.4. Performance Benefits
77.2.5. Cancellation
77.2.6. Throttling
77.2.7. Polyfills
77.2.8. Common Patterns
77.2.9. Debugging
77.2.10. Best Practices

#### **77.3. Animation Libraries**
77.3.1. GSAP
77.3.2. Anime.js
77.3.3. Framer Motion
77.3.4. Lottie
77.3.5. Three.js
77.3.6. CSS-in-JS Animation
77.3.7. Performance Comparison
77.3.8. Bundle Size Considerations
77.3.9. Learning Curve
77.3.10. Selection Criteria

### **Capítulo 78: Intersection Observer**

#### **78.1. Intersection Observer API**
78.1.1. Observer Pattern
78.1.2. Intersection Calculation
78.1.3. Threshold Configuration
78.1.4. Root Margin
78.1.5. Observer Callback
78.1.6. Entry Objects
78.1.7. Performance Benefits
78.1.8. Browser Support
78.1.9. Polyfills
78.1.10. Use Cases

#### **78.2. Lazy Loading**
78.2.1. Image Lazy Loading
78.2.2. Content Lazy Loading
78.2.3. Infinite Scrolling
78.2.4. Performance Optimization
78.2.5. User Experience
78.2.6. SEO Considerations
78.2.7. Accessibility
78.2.8. Error Handling
78.2.9. Testing Strategies
78.2.10. Best Practices

#### **78.3. Scroll Animations**
78.3.1. Scroll-triggered Animations
78.3.2. Parallax Effects
78.3.3. Progress Indicators
78.3.4. Sticky Elements
78.3.5. Performance Optimization
78.3.6. Reduced Motion
78.3.7. Accessibility
78.3.8. Cross-browser Support
78.3.9. Testing
78.3.10. Best Practices

### **Capítulo 79: Mutation Observer**

#### **79.1. Mutation Observer API**
79.1.1. Observer Pattern
79.1.2. Mutation Types
79.1.3. Observer Configuration
79.1.4. Mutation Records
79.1.5. Callback Function
79.1.6. Performance Considerations
79.1.7. Browser Support
79.1.8. Use Cases
79.1.9. Alternatives
79.1.10. Best Practices

#### **79.2. DOM Monitoring**
79.2.1. Element Changes
79.2.2. Attribute Changes
79.2.3. Text Changes
79.2.4. Subtree Monitoring
79.2.5. Performance Impact
79.2.6. Memory Management
79.2.7. Debugging
79.2.8. Testing
79.2.9. Common Patterns
79.2.10. Best Practices

#### **79.3. Casos de Uso Avanzados**
79.3.1. Dynamic Content
79.3.2. Third-party Widgets
79.3.3. A/B Testing
79.3.4. Analytics Tracking
79.3.5. Security Monitoring
79.3.6. Performance Monitoring
79.3.7. Accessibility Monitoring
79.3.8. SEO Optimization
79.3.9. User Behavior Tracking
79.3.10. Best Practices

### **Capítulo 80: Performance en DOM**

#### **80.1. Optimización de DOM**
80.1.1. DOM Performance Basics
80.1.2. Reflow y Repaint
80.1.3. Layout Thrashing
80.1.4. Composite Layers
80.1.5. Critical Rendering Path
80.1.6. DOM Manipulation Optimization
80.1.7. Batch Operations
80.1.8. Document Fragments
80.1.9. Virtual DOM Concepts
80.1.10. Performance Monitoring

#### **80.2. Memory Management**
80.2.1. DOM Memory Leaks
80.2.2. Event Listener Cleanup
80.2.3. Reference Management
80.2.4. Garbage Collection
80.2.5. WeakMap y WeakSet
80.2.6. Memory Profiling
80.2.7. Performance Tools
80.2.8. Best Practices
80.2.9. Common Pitfalls
80.2.10. Optimization Techniques

#### **80.3. Rendering Performance**
80.3.1. Paint y Composite
80.3.2. Layer Creation
80.3.3. Hardware Acceleration
80.3.4. CSS Containment
80.3.5. will-change Property
80.3.6. transform3d Hack
80.3.7. Animation Performance
80.3.8. Scroll Performance
80.3.9. Performance Budgets
80.3.10. Monitoring Tools

### **Capítulo 81: Accesibilidad y ARIA**

#### **81.1. Fundamentos de Accesibilidad**
81.1.1. Web Accessibility Guidelines
81.1.2. WCAG 2.1 Principles
81.1.3. Semantic HTML
81.1.4. Keyboard Navigation
81.1.5. Screen Readers
81.1.6. Focus Management
81.1.7. Color y Contrast
81.1.8. Text Alternatives
81.1.9. Responsive Design
81.1.10. Testing Tools

#### **81.2. ARIA (Accessible Rich Internet Applications)**
81.2.1. ARIA Roles
81.2.2. ARIA Properties
81.2.3. ARIA States
81.2.4. Live Regions
81.2.5. Landmark Roles
81.2.6. Widget Roles
81.2.7. Document Structure
81.2.8. Relationship Properties
81.2.9. Dynamic Content
81.2.10. Best Practices

#### **81.3. JavaScript y Accesibilidad**
81.3.1. Focus Management
81.3.2. Keyboard Event Handling
81.3.3. Dynamic Content Updates
81.3.4. Screen Reader Announcements
81.3.5. Skip Links
81.3.6. Modal Dialogs
81.3.7. Form Validation
81.3.8. Progressive Enhancement
81.3.9. Testing Strategies
81.3.10. Accessibility APIs

#### **81.4. Testing y Herramientas**
81.4.1. Automated Testing
81.4.2. Manual Testing
81.4.3. Screen Reader Testing
81.4.4. Keyboard Testing
81.4.5. Color Contrast Testing
81.4.6. axe-core
81.4.7. Lighthouse Accessibility
81.4.8. WAVE Tool
81.4.9. Pa11y
81.4.10. Accessibility Insights
